name: 🐛 Bug report
description: Report a bug or unexpected behavior
title: "Bug title"
labels: []
body:
  - type: textarea
    validations:
      required: true
    attributes:
      label: Describe the bug
      description: Please enter a clear and short description of your bug here.
      placeholder: A crash occurs if X is called from Y.
  - type: checkboxes
    validations:
      required: true
    attributes:
      label: Affected OS
      description: What operating systems are affected by this bug?
      options:
        - label: Windows
        - label: Linux
        - label: macOS
        - label: Other (specify below)
  - type: textarea
    attributes:
      label: Additional OS information
      description: Please provide any additional details about the affected operating system here, if applicable. You must specify the affected OS and its respective build/version if you have selected the "Windows Insider Preview" or "Other" options.
      placeholder: Windows 11 Insider Preview (10.0.22463.1000.rs_prerelease.210917-1503), Fedora 34
  - type: dropdown
    validations:
      required: true
    attributes:
      label: MsQuic version
      description: On what version of MsQuic does this bug occur? Please select the affected code branch from the drop-down menu.
      options:
        - main
        - v2.3
        - v2.2
        - v2.1
        - v2.0
        - Older
  - type: textarea
    validations:
      required: true
    attributes:
      label: Steps taken to reproduce bug
      description: Add the steps taken to reproduce the bug in this section.
      placeholder: |
        1. Run X
        2. View output
        3. See error
  - type: markdown
    attributes:
      value: |
        ### Behavior
  - type: textarea
    validations:
      required: true
    attributes:
      label: Expected behavior
      description: Enter a description of what you expected to happen.
      placeholder: The call to X from Y should succeed.
  - type: textarea
    validations:
      required: true
    attributes:
      label: Actual outcome
      description: Enter a description of what actually happened when reproducing this issue.
      placeholder: The program crashed after parameter Z was being set in X.
  - type: textarea
    attributes:
      label: Additional details
      description: Please provide any additional details that you would like to share here. If applicable, add screenshots or log output here to help further explain your problem.
