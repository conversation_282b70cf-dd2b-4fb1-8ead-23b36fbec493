name: Netperf

on:
  workflow_dispatch:
  push:
    branches:
    - main
    paths:
    - .github/workflows/netperf.yml
    - .gitmodules
    - scripts/secnetperf.ps1
    - scripts/secnetperf-helpers.psm1
    - scripts/quic_callback.ps1
    - scripts/prepare-machine.ps1
    - scripts/xdp.json
    - src/bin/**
    - src/core/**
    - src/platform/**
    - src/perf/**
    - submodules/quictls/**
    - submodules/openssl/**
    - submodules/xdp-for-windows/**
  pull_request:
    branches:
    - main
    paths:
    - .github/workflows/netperf.yml
    - .gitmodules
    - scripts/secnetperf.ps1
    - scripts/secnetperf-helpers.psm1
    - scripts/quic_callback.ps1
    - scripts/prepare-machine.ps1
    - scripts/xdp.json
    - src/bin/**
    - src/core/**
    - src/platform/**
    - src/perf/**
    - submodules/quictls/**
    - submodules/openssl/**
    - submodules/xdp-for-windows/**

concurrency:
  # Cancel any workflow currently in progress for the same PR.
  # Allow running concurrently with any other commits.
  group: perf-${{ github.event.pull_request.number || github.sha }}
  cancel-in-progress: true

permissions: read-all

jobs:
  run:
    name: Run Perf
    runs-on: windows-latest
    steps:
    - name: Run NetPerf Workflow
      timeout-minutes: 120
      shell: pwsh
      run: |
        $url = "https://raw.githubusercontent.com/microsoft/netperf/main/run-workflow.ps1"
        if ('${{ secrets.NET_PERF_TRIGGER }}' -eq '') {
            Write-Host "Not able to run because no secrets are available!"
            return
        }
        iex "& { $(irm $url) } ${{ secrets.NET_PERF_TRIGGER }} quic ${{ github.sha }} ${{ github.ref }} ${{ github.event.pull_request.number }}"
