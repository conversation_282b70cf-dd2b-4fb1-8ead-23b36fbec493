name: CIFuzz
on: [pull_request]
permissions:
  contents: read
jobs:
  Fuzzing:
    runs-on: ubuntu-latest
    steps:
    - name: Build Fuzzers
      id: build
      uses: google/oss-fuzz/infra/cifuzz/actions/build_fuzzers@5024e8764dc450cd709662afaf569315a4c557e2
      with:
        oss-fuzz-project-name: 'msquic'
        dry-run: false
        language: c
    - name: Run Fuzzers
      uses: google/oss-fuzz/infra/cifuzz/actions/run_fuzzers@5024e8764dc450cd709662afaf569315a4c557e2
      with:
        oss-fuzz-project-name: 'msquic'
        fuzz-seconds: 300
        dry-run: false
        language: c
    - name: Upload Crash
      uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02
      if: failure() && steps.build.outcome == 'success'
      with:
        name: artifacts
        path: ./out/artifacts
