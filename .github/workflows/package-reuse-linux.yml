name: Package Linux

# The caller is responsible for making sure all options passed to this workflow are valid and compatible with each other.

on:
  workflow_dispatch:
  workflow_call:
    inputs:
      config:
        required: false
        default: 'Release'
        type: string
        # options:
        #   - Debug
        #   - Release
      os:
        required: false
        type: string
        default: 'ubuntu-22.04'
        # options:
        #   - ubuntu-22.04
        #   - ubuntu-24.04
      arch:
        required: false
        default: 'x64'
        type: string
        # options:
        #   - x86
        #   - x64
      tls:
        required: false
        default: 'quictls'
        type: string
        # options:
        #   - quictls
        #   - openssl 
      static:
        required: false
        default: ''
        type: string
      clang:
        required: false
        default: ''
        type: string
      sanitize:
        required: false
        default: ''
        type: string
      build:
        required: false
        default: '' # Empty string means build all
        type: string
      xdp:
        required: false
        default: ''
        type: string
      time64:
        required: false
        default: ''
        type: string

permissions: read-all

jobs:
  build-unix-reuse:
    needs: []
    uses: ./.github/workflows/build-reuse-unix.yml
    with:
      config: ${{ inputs.config }}
      os: ${{ inputs.os }}
      arch: ${{ inputs.arch }}
      tls: ${{ inputs.tls }}
      static: ${{ inputs.static }}
      systemcrypto: '-UseSystemOpenSSLCrypto' # packages must use libcrypto on system
      clang: ${{ inputs.clang }}
      build: ${{ inputs.build }}
      xdp: ${{ inputs.xdp }}
      repo: ${{ github.repository }}

  package:
    name: Package
    needs: [build-unix-reuse]
    runs-on: ${{ inputs.os }}
    steps:
    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
      with:
        fetch-depth: 0
    - name: Prepare Machine
      shell: pwsh
      run: scripts/prepare-machine.ps1 -ForBuild ${{ inputs.xdp }}
    - name: Download Build Artifacts
      uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      with:
        name: ${{ inputs.config }}-linux-${{ inputs.os }}-${{ inputs.arch }}-${{ inputs.tls }}-UseSystemOpenSSLCrypto${{ inputs.sanitize }}${{ inputs.xdp }}${{ inputs.build }}
        path: artifacts
    - name: Extract binaries # they are tar'd to preserve permissions and symlinks.
      run: |
        cd artifacts/bin/linux
        rm -rf */
        find -name "*.tar" -exec tar -xvf '{}' \;
    - name: Build Package
      shell: pwsh
      run: scripts/package-distribution.ps1 ${{ inputs.time64 }}
    - name: Upload build artifacts
      uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02
      with:
        name: Package-${{ inputs.config }}-linux-${{ inputs.os }}-${{ inputs.arch }}-${{ inputs.tls }}-UseSystemOpenSSLCrypto${{ inputs.sanitize }}${{ inputs.xdp }}${{ inputs.build }}
        path: artifacts/dist
