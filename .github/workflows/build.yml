name: Build

on:
  workflow_dispatch:
  push:
    branches:
    - main
    - release/*
  pull_request:
    branches:
    - main
    - release/*

concurrency:
  # Cancel any workflow currently in progress for the same PR.
  # Allow running concurrently with any other commits.
  group: build-${{ github.event.pull_request.number || github.sha }}
  cancel-in-progress: true

permissions: read-all

jobs:
  build-windows:
    name: <PERSON>U<PERSON>
    needs: []
    strategy:
      fail-fast: false
      matrix:
        config: ['Debug', 'Release']
        plat: [windows, uwp] # TODO: Support gamecore_console
        os: ['windows-2022']
        arch: [x86, x64, arm64]
        tls: [schannel, quictls, openssl]
        static: ['', '-Static']
        exclude:
        # OpenSSL/quictls doesn't support arm64
        - tls: quictls
          arch: arm64
        - plat: uwp
          tls: openssl
        # TODO: FIX: OpenSSL/quictls build fails with UWP
        - plat: uwp
          tls: quictls
        # TODO: FIX: Static builds fail with UWP
        - plat: uwp
          static: '-Static'
    uses: ./.github/workflows/build-reuse-win.yml
    with:
      config: ${{ matrix.config }}
      plat: ${{ matrix.plat }}
      os: ${{ matrix.os }}
      arch: ${{ matrix.arch }}
      tls: ${{ matrix.tls }}
      static: ${{ matrix.static }}
      repo: ${{ github.repository }}

  build-windows-official:
    name: WinUser-Official
    needs: []
    strategy:
      fail-fast: false
      matrix:
        config: ['Debug', 'Release']
    uses: ./.github/workflows/build-reuse-win.yml
    with:
      config: ${{ matrix.config }}
      plat: 'windows'
      os: 'windows-2022'
      arch: 'x64'
      tls: 'schannel'
      official: '-ForceOfficialRelease'
      repo: ${{ github.repository }}

  build-windows-kernel:
    name: WinKernel
    needs: []
    strategy:
      fail-fast: false
      matrix:
        config: ['Debug', 'Release']
        plat: [winkernel]
        os: ['windows-2022']
        arch: [x64, arm64]
        tls: [schannel]
    uses: ./.github/workflows/build-reuse-winkernel.yml
    with:
      config: ${{ matrix.config }}
      plat: ${{ matrix.plat }}
      os: ${{ matrix.os }}
      arch: ${{ matrix.arch }}
      tls: ${{ matrix.tls }}
      repo: ${{ github.repository }}

  build-ubuntu-cross-compile:
    name: UbuntuArm
    needs: []
    strategy:
      fail-fast: false
      matrix:
        config: ['Debug', 'Release']
        plat: [linux]
        os: ['ubuntu-22.04', 'ubuntu-24.04']
        arch: [arm, arm64]
        tls: [quictls, openssl]
        static: ['', '-Static']
    uses: ./.github/workflows/build-reuse-unix.yml
    with:
      config: ${{ matrix.config }}
      plat: ${{ matrix.plat }}
      os: ${{ matrix.os }}
      arch: ${{ matrix.arch }}
      tls: ${{ matrix.tls }}
      static: ${{ matrix.static }}
      repo: ${{ github.repository }}

  build-ubuntu:
    name: Ubuntu
    needs: []
    strategy:
      fail-fast: false
      matrix:
        config: ['Debug', 'Release']
        plat: [linux, android]
        os: ['ubuntu-22.04', 'ubuntu-24.04']
        arch: [x86, x64]
        tls: [quictls, openssl]
        systemcrypto: ['', '-UseSystemOpenSSLCrypto']
        static: ['', '-Static']
        clang: ['', '-Clang']
        codecheck: ['', '-CodeCheck']
        xdp: ['', '-UseXdp']
        exclude:
        # Android doesn't support x86, XDP, Clang, CodeCheck or SystemCrypto
        - plat: android
          arch: x86
        - plat: android
          systemcrypto: '-UseSystemOpenSSLCrypto'
        # No openssl system crypto on ubuntu-22.04
        - os: 'ubuntu-22.04'
          tls: 'quictls'
          systemcrypto: '-UseSystemOpenSSLCrypto'
        # No openssl system crypto on ubuntu-24.04
        - os: 'ubuntu-24.04'
          tls: 'quictls'
          systemcrypto: '-UseSystemOpenSSLCrypto'
        # No openssl system crypto on ubuntu-22.04
        - os: 'ubuntu-22.04'
          tls: 'openssl'
          systemcrypto: '-UseSystemOpenSSLCrypto'
        # No openssl system crypto on ubuntu-24.04
        - os: 'ubuntu-24.04'
          tls: 'openssl'
          systemcrypto: '-UseSystemOpenSSLCrypto'
        # linux xdp is for ubuntu24.04 only for now
        - plat: android
          xdp: "-UseXdp"
        - plat: android
          clang: '-Clang'
        - plat: android
          codecheck: '-CodeCheck'
        # CodeCheck doesn't work with SystemCrypto, Clang, Static or Release builds
        - codecheck: '-CodeCheck'
          systemcrypto: '-UseSystemOpenSSLCrypto'
        - codecheck: '-CodeCheck'
          clang: '-Clang'
        - codecheck: '-CodeCheck'
          static: '-Static'
        - codecheck: '-CodeCheck'
          config: Release
        # Static build can't dynamically link to libcrypto
        - static: '-Static'
          systemcrypto: '-UseSystemOpenSSLCrypto'
        # No openssl system crypto on ubuntu 22.04 or 24.04
        - os: 'ubuntu-22.04'
          systemcrypto: '-UseSystemOpenSSLCrypto'
        - os: 'ubuntu-24.04'
          systemcrypto: '-UseSystemOpenSSLCrypto'
        # Linux xdp is for x64 ubuntu24.04 only for now
        - xdp: "-UseXdp"
          os: 'ubuntu-22.04'
        - xdp: "-UseXdp"
          arch: x86
    uses: ./.github/workflows/build-reuse-unix.yml
    with:
      config: ${{ matrix.config }}
      plat: ${{ matrix.plat }}
      os: ${{ matrix.os }}
      arch: ${{ matrix.arch }}
      tls: ${{ matrix.tls }}
      systemcrypto: ${{ matrix.systemcrypto }}
      static: ${{ matrix.static }}
      clang: ${{ matrix.clang }}
      codecheck: ${{ matrix.codecheck }}
      xdp: ${{ matrix.xdp }}
      repo: ${{ github.repository }}

  build-darwin:
    name: MacOs
    needs: []
    strategy:
      fail-fast: false
      matrix:
        config: ['Debug', 'Release']
        plat: [macos, ios]
        os: ['macos-13']
        arch: [x64, arm64]
        tls: [quictls, openssl]
        static: ['', '-Static']
    uses: ./.github/workflows/build-reuse-unix.yml
    with:
      config: ${{ matrix.config }}
      plat: ${{ matrix.plat }}
      os: ${{ matrix.os }}
      arch: ${{ matrix.arch }}
      tls: ${{ matrix.tls }}
      static: ${{ matrix.static }}
      repo: ${{ github.repository }}

  build-nuget:
    name: Build Nuget Package
    needs: [build-windows]
    strategy:
      fail-fast: false
      matrix:
        vec: [
          #{ plat: "uwp",  tls: "quictls", arg: "-UWP" }, # TODO: Fix build first
          { plat: "windows",  tls: "quictls" },
          { plat: "windows",  tls: "openssl" },
          { plat: "windows",  tls: "schannel" },
        ]
    runs-on: windows-2022
    steps:
    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
    - name: Download Build Artifacts
      uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      with:
        name: Release-${{ matrix.vec.plat }}-windows-2022-x86-${{ matrix.vec.tls }}
        path: artifacts
    - name: Download Build Artifacts
      uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      with:
        name: Release-${{ matrix.vec.plat }}-windows-2022-x64-${{ matrix.vec.tls }}
        path: artifacts
    - name: Download Build Artifacts
      uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      if: matrix.vec.tls != 'quictls' # TODO: Remove once quictls is supported
      with:
        name: Release-${{ matrix.vec.plat }}-windows-2022-arm64-${{ matrix.vec.tls }}
        path: artifacts
    - name: Build Package
      shell: pwsh
      run: scripts/package-nuget.ps1 -Tls ${{ matrix.vec.tls }} ${{ matrix.vec.arg }} -GHA
    - name: Upload build artifacts
      uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02
      with:
        name: Nuget-Release-${{ matrix.vec.plat }}-windows-2022-${{ matrix.vec.tls }}
        path: artifacts/dist/*.nupkg

  Complete:
    name: Build Complete
    if: always()
    needs: [build-windows, build-windows-official, build-windows-kernel, build-ubuntu-cross-compile, build-ubuntu, build-darwin, build-nuget]
    runs-on: ubuntu-latest
    permissions: {} # No need for any permissions.
    steps:
    - name: Decide whether the needed jobs succeeded or failed
      uses: re-actors/alls-green@05ac9388f0aebcb5727afa17fcccfecd6f8ec5fe
      with:
        jobs: ${{ toJSON(needs) }}
