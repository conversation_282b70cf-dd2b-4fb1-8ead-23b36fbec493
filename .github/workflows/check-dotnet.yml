name: CheckDotnet

on:
  push:
    branches:
    - main
    - release/*
  pull_request:
    branches:
    - main
    - release/*

permissions: read-all

jobs:
  validate:
    permissions:
      contents: read # for actions/checkout to fetch code
    name: Validate
    runs-on: windows-latest
    steps:
    - name: Setup .NET
      uses: actions/setup-dotnet@67a3573c9a986a3f9c594539f4ab511d57bb3ce9
      with:
        dotnet-version: 6.0.x
    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
    - name: Prepare Machine
      run: scripts/prepare-machine.ps1
      shell: pwsh
    - name: Run DotNet Update
      run: scripts/generate-dotnet.ps1
      shell: pwsh
    - name: Check for Changes
      run: |
        git add .
        git --no-pager diff --exit-code HEAD
