name: Plugins

on:
  push:
    branches: [ main ]
    paths:
    - .github/workflows/plugins.yml
    - src/plugins/**
  pull_request:
    branches: [ main ]
    paths:
    - .github/workflows/plugins.yml
    - src/plugins/**

permissions: read-all

jobs:
  build_dbgext:
    name: Build WinDbg Extension
    runs-on: windows-2022
    strategy:
      fail-fast: false
      matrix:
        configuration: [Release, Debug]
        platform: [x86, x64]
    steps:
    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
    - name: Setup MSBuild.exe
      uses: microsoft/setup-msbuild@6fb02220983dee41ce7ae257b6f4d8f9bf5ed4ce
    - name: Build
      run: msbuild src\plugins\msquic.windbg.sln /p:configuration=${{ matrix.configuration }} /p:platform=${{ matrix.platform }}
    - uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02
      with:
        name: bin_windbg_${{ matrix.configuration }}_${{ matrix.platform }}
        path: |
          artifacts/bin
          !artifacts/bin/**/*.ilk
          !artifacts/bin/**/*.exp
          !artifacts/bin/**/*.lib

  build_quictrace:
    name: Build QuicTrace
    needs: []
    runs-on: windows-latest
    strategy:
      fail-fast: false
      matrix:
        configuration: [Release, Debug]
    steps:
    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
    - name: Setup .NET
      uses: actions/setup-dotnet@67a3573c9a986a3f9c594539f4ab511d57bb3ce9
      with:
        dotnet-version: 6.0.x
    - name: Install Dependencies
      run: dotnet tool install --global Microsoft.Performance.Toolkit.Plugins.Cli --version 0.1.25-preview
    - name: Build
      run: dotnet build src\plugins\QuicTrace.sln -c ${{ matrix.configuration }}
    - uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02
      with:
        name: bin_quictrace_${{ matrix.configuration }}
        path: artifacts/bin/quictrace/${{ matrix.configuration }}
    - name: Package
      shell: pwsh
      run: |
        $sourceDir = "src\plugins\trace\dll\bin\${{ matrix.configuration }}\netstandard2.1"
        $manifestPath = "$sourceDir/pluginManifest.json"
        $manifest = Get-Content -Path $manifestPath | ConvertFrom-Json
        $pluginId = $manifest.identity.id
        $pluginVersion = $manifest.identity.version
        $packageName = "$pluginId-$pluginVersion.ptix"
        Write-Host "Creating $packageName"
        & plugintool pack -s $sourceDir -o "artifacts/bin/quictrace/$packageName"
    - uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02
      with:
        name: ptix_quictrace_${{ matrix.configuration }}
        path: artifacts/bin/quictrace/*.ptix
