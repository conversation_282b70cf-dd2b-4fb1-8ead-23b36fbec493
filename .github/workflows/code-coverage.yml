name: Code Coverage

on:
  workflow_dispatch:
  push:
    branches:
    - main
    - release/*
  pull_request:
    branches:
    - main
    - release/*

concurrency:
  # Cancel any workflow currently in progress for the same PR.
  # Allow running concurrently with any other commits.
  group: codecoverage-${{ github.event.pull_request.number || github.sha }}
  cancel-in-progress: true

permissions: read-all

jobs:
  build-windows:
    name: Build WinUser
    strategy:
      fail-fast: false
      matrix:
        vec: [
          { config: "Debug", plat: "windows", os: "windows-2022", arch: "x64", tls: "quictls", build: "-Test" },
        ]
    uses: ./.github/workflows/build-reuse-win.yml
    with:
      config: ${{ matrix.vec.config }}
      plat: ${{ matrix.vec.plat }}
      os: ${{ matrix.vec.os }}
      arch: ${{ matrix.vec.arch }}
      tls: ${{ matrix.vec.tls }}
      sanitize: ${{ matrix.vec.sanitize }}
      build: ${{ matrix.vec.build }}
      repo: ${{ github.repository }}

  bvt-winlatest:
    name: BVT WinPrerelease
    needs: [build-windows]
    strategy:
      fail-fast: false
      matrix:
        vec: [
          { config: "Debug", plat: "windows", os: "windows-2022", arch: "x64", tls: "quictls", build: "-Test" },
          { config: "Debug", plat: "windows", os: "windows-2022", arch: "x64", tls: "quictls", build: "-Test", xdp: "-UseXdp" },
        ]
    runs-on:
     - self-hosted
     - "1ES.Pool=1es-msquic-pool"
     - "1ES.ImageOverride=WinServerPrerelease-LatestPwsh"
    steps:
    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
      with:
        fetch-depth: 0
    - name: Download Build Artifacts
      uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      with:
        name: ${{ matrix.vec.config }}-${{ matrix.vec.plat }}-${{ matrix.vec.os }}-${{ matrix.vec.arch }}-${{ matrix.vec.tls }}${{ matrix.vec.sanitize }}${{ matrix.vec.build }}
        path: artifacts
    - name: Prepare Machine
      run: scripts/prepare-machine.ps1 -Tls ${{ matrix.vec.tls }} -ForTest ${{ matrix.vec.xdp }} -InstallCodeCoverage
      shell: pwsh
    - name: Install ETW Manifest
      shell: pwsh
      run: |
        $MsQuicDll = ".\artifacts\bin\windows\${{ matrix.vec.arch }}_${{ matrix.vec.config }}_${{ matrix.vec.tls }}\msquic.dll"
        $ManifestPath = ".\src\manifest\MsQuicEtw.man"
        wevtutil.exe um $ManifestPath
        wevtutil.exe im $ManifestPath /rf:$($MsQuicDll) /mf:$($MsQuicDll)
    - name: Test
      shell: pwsh
      timeout-minutes: 120
      continue-on-error: true
      run: scripts/test.ps1 -NoProgress -IsolationMode Batch -SkipUnitTests -CodeCoverage -Config ${{ matrix.vec.config }} -Arch ${{ matrix.vec.arch }} -Tls ${{ matrix.vec.tls }} -LogProfile Full.Light ${{ matrix.vec.xdp }} ${{ matrix.vec.qtip }}
    - name: Upload Results
      uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02
      with:
        name: BVT-${{ matrix.vec.config }}-${{ matrix.vec.plat }}-${{ matrix.vec.os }}-${{ matrix.vec.arch }}-${{ matrix.vec.tls }}${{ matrix.vec.xdp }}${{ matrix.vec.qtip }}${{ matrix.vec.systemcrypto }}${{ matrix.vec.sanitize }}
        path: artifacts/coverage/*.cov

  stress-winlatest:
    name: Stress WinPrerelease
    needs: [build-windows]
    strategy:
      fail-fast: false
      matrix:
        vec: [
          { config: "Debug", plat: "windows", os: "windows-2022", arch: "x64", tls: "quictls", build: "-Test" },
        ]
    runs-on:
    - self-hosted
    - "1ES.Pool=1es-msquic-pool"
    - "1ES.ImageOverride=WinServerPrerelease-LatestPwsh"
    env:
      main-timeout: 3600000
      main-repeat: 100
      main-allocfail: 100
      pr-timeout: 600000
      pr-repeat: 20
      pr-allocfail: 100
    steps:
    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
      with:
        fetch-depth: 0
    - uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      with:
        name: ${{ matrix.vec.config }}-${{ matrix.vec.plat }}-${{ matrix.vec.os }}-${{ matrix.vec.arch }}-${{ matrix.vec.tls }}${{ matrix.vec.sanitize }}${{ matrix.vec.build }}
        path: artifacts
    - name: Prepare Machine
      run: scripts/prepare-machine.ps1 -Tls ${{ matrix.vec.tls }} -ForTest ${{ matrix.vec.xdp }} -InstallCodeCoverage
      shell: pwsh
    - name: spinquic (PR)
      if: github.event_name == 'pull_request'
      timeout-minutes: 15
      continue-on-error: true
      shell: pwsh
      run: scripts/spin.ps1 -Config ${{ matrix.vec.config }} -Arch ${{ matrix.vec.arch }} -Tls ${{ matrix.vec.tls }} -Timeout ${{ env.pr-timeout }} -RepeatCount ${{ env.pr-repeat }} -AllocFail ${{ env.pr-allocfail }} ${{ matrix.vec.xdp }} -CodeCoverage -LogProfile Basic.Light
    - name: spinquic (Official)
      if: github.event_name != 'pull_request'
      timeout-minutes: 65
      continue-on-error: true
      shell: pwsh
      run: scripts/spin.ps1 -Config ${{ matrix.vec.config }} -Arch ${{ matrix.vec.arch }} -Tls ${{ matrix.vec.tls }} -Timeout ${{ env.main-timeout }} -RepeatCount ${{ env.main-repeat }} -AllocFail ${{ env.main-allocfail }} ${{ matrix.vec.xdp }} -CodeCoverage -LogProfile Basic.Light
    - name: Upload Results
      uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02
      with:
        name: Spin-${{ matrix.vec.config }}-${{ matrix.vec.plat }}-${{ matrix.vec.os }}-${{ matrix.vec.arch }}-${{ matrix.vec.tls }}${{ matrix.vec.xdp }}${{ matrix.vec.sanitize }}
        path: artifacts/coverage/*.cov

  recvfuzz-winlatest:
    name: Recv Fuzz WinPrerelease
    needs: [build-windows]
    strategy:
      fail-fast: false
      matrix:
        vec: [
          { config: "Debug", plat: "windows", os: "windows-2022", arch: "x64", tls: "quictls", build: "-Test" },
        ]
    runs-on:
    - self-hosted
    - "1ES.Pool=1es-msquic-pool"
    - "1ES.ImageOverride=WinServerPrerelease-LatestPwsh"
    env:
      main-timeout: 3600000
      pr-timeout: 600000
    steps:
    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
      with:
        fetch-depth: 0
    - uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      with:
        name: ${{ matrix.vec.config }}-${{ matrix.vec.plat }}-${{ matrix.vec.os }}-${{ matrix.vec.arch }}-${{ matrix.vec.tls }}${{ matrix.vec.sanitize }}${{ matrix.vec.build }}
        path: artifacts
    - name: Prepare Machine
      run: scripts/prepare-machine.ps1 -Tls ${{ matrix.vec.tls }} -ForTest ${{ matrix.vec.xdp }} -InstallCodeCoverage
      shell: pwsh
    - name: recvfuzz (PR)
      if: github.event_name == 'pull_request'
      timeout-minutes: 15
      continue-on-error: true
      shell: pwsh
      run: scripts/recvfuzz.ps1 -Config ${{ matrix.vec.config }} -Arch ${{ matrix.vec.arch }} -Tls ${{ matrix.vec.tls }} -timeout ${{ env.pr-timeout }} ${{ matrix.vec.xdp }} -CodeCoverage -LogProfile Basic.Light
    - name: recvfuzz (Official)
      if: github.event_name != 'pull_request'
      timeout-minutes: 65
      continue-on-error: true
      shell: pwsh
      run: scripts/recvfuzz.ps1 -Config ${{ matrix.vec.config }} -Arch ${{ matrix.vec.arch }} -Tls ${{ matrix.vec.tls }} -timeout ${{ env.main-timeout }} ${{ matrix.vec.xdp }} -CodeCoverage -LogProfile Basic.Light
    - name: Upload Results
      uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02
      with:
        name: RecvFuzz-${{ matrix.vec.config }}-${{ matrix.vec.plat }}-${{ matrix.vec.os }}-${{ matrix.vec.arch }}-${{ matrix.vec.tls }}${{ matrix.vec.xdp }}${{ matrix.vec.sanitize }}
        path: artifacts/coverage/*.cov

  merge-coverage:
    name: Merge Coverage
    needs: [bvt-winlatest, stress-winlatest, recvfuzz-winlatest]
    runs-on: windows-2022
    steps:
    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
      with:
        fetch-depth: 0
    - name: Prepare Machine
      run: scripts/prepare-machine.ps1 -ForTest -InstallCodeCoverage
      shell: pwsh
    - name: Create Folder for Artifacts
      shell: pwsh
      run: |
        New-Item -ItemType Directory -Force -Path artifacts\coverage\windows\x64_Debug_quictls
    - uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      name: Download BVT Coverage Results
      with:
        name: BVT-Debug-windows-windows-2022-x64-quictls
        path: artifacts\coverage\windows\x64_Debug_quictls
    - uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      name: Download BVT (XDP) Coverage Results
      with:
        name: BVT-Debug-windows-windows-2022-x64-quictls-UseXdp
        path: artifacts\coverage\windows\x64_Debug_quictls
    - uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      name: Download SpinQuic Coverage Results
      with:
        name: Spin-Debug-windows-windows-2022-x64-quictls
        path: artifacts\coverage\windows\x64_Debug_quictls
    - uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      name: Download RecvFuzz Coverage Results
      with:
        name: RecvFuzz-Debug-windows-windows-2022-x64-quictls
        path: artifacts\coverage\windows\x64_Debug_quictls
    - name: Dir Folder for Artifacts
      shell: pwsh
      run: |
        dir artifacts\coverage\windows\x64_Debug_quictls
    - name: Merge
      shell: pwsh
      run: scripts/merge-coverage.ps1 -Config Debug -Arch x64 -Tls quictls
    - name: Upload Results
      uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02
      with:
        name: merged
        path: artifacts\coverage\windows\x64_Debug_quictls\msquiccoverage.xml

  publish-coverage:
    name: Generate Summary
    needs: [merge-coverage]
    runs-on: ubuntu-22.04
    steps:
    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
    - uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      name: Download Merged Coverage Report
      with:
        name: merged
    - name: Upload coverage reports to Codecov
      uses: codecov/codecov-action@18283e04ce6e62d37312384ff67231eb8fd56d24
      with:
        fail_ci_if_error: true
        files: msquiccoverage.xml
        token: ${{ secrets.CODECOV_TOKEN }}
    - name: Code Coverage Summary Report
      uses: irongut/CodeCoverageSummary@51cc3a756ddcd398d447c044c02cb6aa83fdae95
      with:
        filename: msquiccoverage.xml
        badge: true
        format: 'markdown'
        output: 'both'
    - name: Write to Job Summary
      run: cat code-coverage-results.md >> $GITHUB_STEP_SUMMARY
