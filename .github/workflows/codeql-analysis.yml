---
name: "CodeQL"

on:
  push:
    branches:
      - main
      - release/*
  pull_request:
    # The branches below must be a subset of the branches above
    branches:
      - main
      - release/*
  schedule:
    - cron: '0 17 * * 1'

permissions: read-all

jobs:
  analyze:
    permissions:
      actions: read # for github/codeql-action/init to get workflow details
      contents: read # for actions/checkout to fetch code
      security-events: write # for github/codeql-action/analyze to upload SARIF results
    name: Analyze
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          fetch-depth: 2
          submodules: 'recursive'

      # Install dependencies and build submodules before starting analysis.
      - run: |
         sudo apt-add-repository ppa:lttng/stable-2.13
         sudo apt-add-repository "deb http://mirrors.kernel.org/ubuntu noble main" -y
         sudo apt-get update
         sudo apt-get install -y liblttng-ust-dev libnuma-dev
         sudo apt-get install -y libxdp-dev libbpf-dev libnl-3-dev libnl-genl-3-dev libnl-route-3-dev zlib1g-dev zlib1g pkg-config m4 clang libpcap-dev libelf-dev
         sudo apt-get install -y --no-install-recommends libc6-dev-i386
         sudo sh scripts/install-powershell-docker.sh
         mkdir build
         cd build
         cmake ..
         cmake --build . --target OpenSSL_Target

      - name: Initialize CodeQL
        uses: github/codeql-action/init@ce28f5bb42b7a9f2c824e633a3f6ee835bab6858
        with:
          languages: cpp
          config-file: ./.github/codeql/codeql-config.yml

      # Analyze this build.
      - run: |
         cd build
         cmake --build .

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@ce28f5bb42b7a9f2c824e633a3f6ee835bab6858
