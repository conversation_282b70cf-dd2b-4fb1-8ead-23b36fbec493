name: Test Down Level

on:
  push:
    branches:
    - main
    - release/*
  pull_request:
    branches:
    - main
    - release/*

concurrency:
  # Cancel any workflow currently in progress for the same PR.
  # Allow running concurrently with any other commits.
  group: testdownlevel-${{ github.event.pull_request.number || github.sha }}
  cancel-in-progress: true

permissions: read-all

jobs:
  test-down-level:
    strategy:
      fail-fast: false
      matrix:
        vec: [
          # v2.3
          { release: "2.3.11",  os: "ubuntu-22.04", arch: "x64", tls: "quictls", imgtls: "openssl3" },
          { release: "2.3.11",  os: "windows-2022", arch: "x64", tls: "schannel", imgtls: "schannel" },
          { release: "2.3.11",  os: "windows-2022", arch: "x64", tls: "quictls", imgtls: "openssl3" },
          # v2.4
          { release: "2.4.10",  os: "ubuntu-22.04", arch: "x64", tls: "quictls", imgtls: "openssl3" },
          { release: "2.4.10",  os: "ubuntu-24.04", arch: "x64", tls: "quictls", imgtls: "openssl3" },
          { release: "2.4.10",  os: "windows-2022", arch: "x64", tls: "schannel", imgtls: "schannel" },
          { release: "2.4.10",  os: "windows-2022", arch: "x64", tls: "quictls", imgtls: "openssl3" },
          { release: "2.4.10",  os: "windows-2025", arch: "x64", tls: "schannel", imgtls: "schannel" },
          { release: "2.4.10",  os: "windows-2025", arch: "x64", tls: "quictls", imgtls: "openssl3" },
        ]
    runs-on: ${{ matrix.vec.os }}
    name: Test
    steps:
    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
    - name: Install Perl
      if: runner.os == 'Windows'
      uses: shogo82148/actions-setup-perl@22423f01bde48fb88785c007e3166fbbbd8e892a
      with:
        perl-version: '5.34'
    - name: Install NASM
      if: runner.os == 'Windows'
      uses: ilammy/setup-nasm@72793074d3c8cdda771dba85f6deafe00623038b
    - name: Prepare Machine
      run: scripts/prepare-machine.ps1 -Tls ${{ matrix.vec.tls }} -DisableTest
      shell: pwsh
    - name: Build Release
      shell: pwsh
      run: scripts/build.ps1 -Config Release -Arch ${{ matrix.vec.arch }} -Tls ${{ matrix.vec.tls }} -UseSystemOpenSSLCrypto -DisableTest -DisableTools -DisablePerf
    - name: Download Tests
      shell: pwsh
      run: |
        $osName = "${{runner.os}}".ToLower()
        Invoke-WebRequest -Uri "https://github.com/microsoft/msquic/releases/download/v${{matrix.vec.release}}/msquic_${{runner.os}}_${{matrix.vec.arch}}_Release_${{matrix.vec.imgtls}}_test.zip" -OutFile "artifacts/test.zip"
        Expand-Archive -Path artifacts/test.zip -DestinationPath artifacts/bin/$osName/${{matrix.vec.arch}}_Release_${{matrix.vec.tls}}
    - name: Run Tests (Linux)
      if: runner.os == 'Linux'
      shell: pwsh
      run: |
        chmod +x artifacts/bin/linux/${{matrix.vec.arch}}_Release_${{matrix.vec.tls}}/msquictest
        $env:LD_LIBRARY_PATH = Join-Path (Get-Location).Path "artifacts/bin/linux/${{matrix.vec.arch}}_Release_${{matrix.vec.tls}}"
        scripts/test.ps1 -AZP -Config Release -Arch ${{ matrix.vec.arch }} -Tls ${{ matrix.vec.tls }} -SkipUnitTests -Filter -*CredValidation*:*ConnectClientCertificate*:Basic.StartTwoListenersSameALPN:ParameterValidation.ValidateGlobalParam
    - name: Run Tests (Windows)
      if: runner.os == 'Windows'
      run: scripts/test.ps1 -AZP -Config Release -Arch ${{ matrix.vec.arch }} -Tls ${{ matrix.vec.tls }} -SkipUnitTests -Filter -*ValidateConfiguration:*ValidAlpnLengths:*ResumeRejection*:*ConnectClientCertificate*:Basic.StartTwoListenersSameALPN:ParameterValidation.ValidateGlobalParam

  Complete:
    name: Down Level Complete
    if: always()
    needs: [test-down-level]
    runs-on: ubuntu-latest
    permissions: {} # No need for any permissions.
    steps:
    - name: Decide whether the needed jobs succeeded or failed
      uses: re-actors/alls-green@05ac9388f0aebcb5727afa17fcccfecd6f8ec5fe
      with:
        jobs: ${{ toJSON(needs) }}
