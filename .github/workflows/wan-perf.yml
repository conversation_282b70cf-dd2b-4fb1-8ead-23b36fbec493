name: WAN Perf

on:
  workflow_dispatch:
#  push:
#    branches:
#    - main
#    paths:
#    - .github/workflows/wan-perf.yml
#    - src/core/*
#    - src/platform/*
#    - src/perf/*
#  pull_request:
#    branches:
#    - main
#    paths:
#    - .github/workflows/wan-perf.yml
#    - src/core/*
#    - src/platform/*
#    - src/perf/*
#    - submodules/quictls/*
concurrency:
  # Cancel any workflow currently in progress for the same PR.
  # Allow running concurrently with any other commits.
  group: wanperf-${{ github.event.pull_request.number || github.sha }}
  cancel-in-progress: true

permissions: read-all

jobs:
  build-perf:
    permissions:
      contents: read # for actions/checkout to fetch code
    name: Build Perf
    runs-on: windows-latest
    steps:
    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
    - name: Prepare Machine
      shell: pwsh
      run: scripts/prepare-machine.ps1 -ForBuild -DisableTest
    - name: Prepare Machine
      shell: pwsh
      run: scripts/build.ps1 -Config Release -DisableTest -DisableTools
    - uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02
      with:
        name: bin
        path: |
          artifacts/bin
          !artifacts/bin/**/*.ilk
          !artifacts/bin/**/*.cer
          !artifacts/bin/**/*.exp
          !artifacts/bin/**/*.lastcodeanalysissucceeded
          !artifacts/bin/**/*.pgd
          !artifacts/bin/**/*.lib
  wan-perf:
    permissions:
      contents: read # for actions/checkout to fetch code
    name: Run Tests
    runs-on: windows-2022
    needs: build-perf
    env:
      seed: 41473a2e60b6958500ec0add7dcfb9 # TODO - Randomize?
      iterations: 3
      duration: 10000
      pacing: 1
      reorder: "(0,1000)"
      delay: "(0,5,10)"
      congestionControl: "(\"cubic\", \"bbr\")"
      loss: "(0,1000,10000)"
    strategy:
      fail-fast: false
      matrix:
        rate: [10, 50, 100, 1000]
        rtt: [5, 50, 200]
        queueRatio: [0.2, 1, 5]
        exclude:
        - rate: 5
          rtt: 5
          queueRatio: 0.2 # Results in sub-packet limit
        - rate: 1000
          rtt: 500
          queueRatio: 5 # Exceeds QueueLimitPackets limit of 100000
    steps:
    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
    - name: Prepare Machine
      shell: pwsh
      run: scripts/prepare-machine.ps1 -ForTest -InstallDuoNic
    - uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      with:
        name: bin
        path: artifacts/bin
    - name: Run WAN Perf (QUIC only)
      if: ${{ github.event_name == 'pull_request' || github.event_name == 'workflow_dispatch' }}
      shell: pwsh
      run: scripts/emulated-performance.ps1 -Debug -Protocol QUIC -LogProfile Performance.Light -NoDateLogDir -NumIterations ${{ env.iterations }} -DurationMs ${{ env.duration }} -Pacing ${{ env.pacing }} -BottleneckMbps ${{ matrix.rate }} -RttMs ${{ matrix.rtt }} -BottleneckQueueRatio ${{ matrix.queueRatio }} -RandomLossDenominator ${{ env.loss }} -RandomReorderDenominator ${{ env.reorder }} -ReorderDelayDeltaMs ${{ env.delay }} -BaseRandomSeed ${{ env.seed }} -CongestionControl ${{ env.congestionControl }}
    - name: Run WAN Perf (QUIC + TCP)
      if: ${{ github.event_name != 'pull_request' && github.event_name != 'workflow_dispatch' }}
      shell: pwsh
      run: scripts/emulated-performance.ps1 -Debug -Protocol ('QUIC','TCPTLS') -LogProfile Performance.Light -NoDateLogDir -NumIterations ${{ env.iterations }} -DurationMs ${{ env.duration }} -Pacing ${{ env.pacing }} -BottleneckMbps ${{ matrix.rate }} -RttMs ${{ matrix.rtt }} -BottleneckQueueRatio ${{ matrix.queueRatio }} -RandomLossDenominator ${{ env.loss }} -RandomReorderDenominator ${{ env.reorder }} -ReorderDelayDeltaMs ${{ env.delay }} -BaseRandomSeed ${{ env.seed }} -CongestionControl ${{ env.congestionControl }}
    - name: Upload Results
      uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02
      with:
        name: data
        path: artifacts/PerfDataResults/windows/x64_Release_schannel/WAN/*.json
    - name: Upload Logs
      uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02
      with:
        name: ${{ format('logs.{0}mbps.{1}ms', matrix.rate, matrix.rtt) }}
        path: artifacts/logs/wanperf/*.etl
  merge-data:
    permissions: write-all
    name: Merge Results
    runs-on: windows-2022
    needs: wan-perf
    steps:
    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
      with:
        fetch-depth: 0
    - uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      with:
        name: data
        path: artifacts/PerfDataResults/windows/x64_Release_schannel/WAN
    - name: Merge Data Files
      shell: pwsh
      run: scripts/emulated-performance.ps1 -MergeDataFiles
    - name: Upload CSV
      uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02
      with:
        name: csv
        path: artifacts\PerfDataResults\windows\x64_Release_schannel\WAN\wan_data.csv
