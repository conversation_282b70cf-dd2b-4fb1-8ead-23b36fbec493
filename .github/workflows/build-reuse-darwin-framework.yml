name: Build Darwin Universal

on:
  workflow_call:
    inputs:
      ref:
        required: false
        default: ''
        type: string
      repo:
        required: false
        default: microsoft/msquic
        type: string
      config:
        required: false
        default: 'Release'
        type: string
        # options:
        #   - Debug
        #   - Release
      tls:
        required: false
        default: 'quictls'
        type: string
        # options:
        #   - quictls
        #   - openssl 
      static:
        required: false
        default: ''
        type: string

permissions: read-all

jobs:
  build-darwin:
    name: Build Darwin Binaries
    needs: []
    strategy:
      fail-fast: false
      matrix:
        plat: [macos, ios]
        arch: [x64, arm64]
    uses: ./.github/workflows/build-reuse-unix.yml
    with:
      ref: ${{ inputs.ref }}
      config: ${{ inputs.config }}
      plat: ${{ matrix.plat }}
      os: macos-13
      arch: ${{ matrix.arch }}
      tls: ${{ inputs.tls }}
      static: ${{ inputs.static }}
      repo: ${{ inputs.repo }}

  build-darwin-universal:
    name: Build Universal Binaries
    needs: [build-darwin]
    runs-on: macos-13
    steps:
    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
      with:
        repository: ${{ inputs.repo}}
        ref: ${{ inputs.ref }}
    - name: Download Build Artifacts (x64)
      uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      with:
        name: ${{ inputs.config }}-macos-macos-13-x64-${{ inputs.tls }}${{ inputs.static }}
        path: artifacts
    - name: Download Build Artifacts (arm64)
      uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      with:
        name: ${{ inputs.config }}-macos-macos-13-arm64-${{ inputs.tls }}${{ inputs.static }}
        path: artifacts
    - name: Build Package
      shell: pwsh
      run: scripts/merge-darwin.ps1 -DeleteSource -Config ${{ inputs.config }} -Tls ${{ inputs.tls }}
    - name: Upload build artifacts
      uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02
      with:
        name: ${{ inputs.config }}-macos-macos-13-universal-${{ inputs.tls }}${{ inputs.static }}
        path: artifacts

  build-darwin-framework:
    name: Build Darwin Framework
    needs: [build-darwin-universal]
    strategy:
      fail-fast: false
      matrix:
        vec: [
          { plat: "ios", arch: "x64" }, # iOS Simulator
          { plat: "ios", arch: "arm64" },
          { plat: "macos", arch: "universal" },
        ]
    runs-on: macos-13
    steps:
    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
      with:
        repository: ${{ inputs.repo}}
        ref: ${{ inputs.ref }}
    - name: Download Build Artifacts (x64)
      uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      with:
        name: ${{ inputs.config }}-${{ matrix.vec.plat }}-macos-13-${{ matrix.vec.arch }}-${{ inputs.tls }}${{ inputs.static }}
        path: artifacts
    - name: Build Framework
      shell: pwsh
      run: scripts/package-darwin-framework.ps1 -Config ${{ inputs.config }} -Platform ${{ matrix.vec.plat }} -Arch ${{ matrix.vec.arch }} -Tls ${{ inputs.tls }}
    - name: Upload build artifacts
      uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02
      with:
        name: Framework-${{ inputs.config }}-${{ matrix.vec.plat }}-macos-13-${{ matrix.vec.arch }}-${{ inputs.tls }}${{ inputs.static }}
        path: artifacts

  build-darwin-xcframework:
    name: Build Darwin XCFramework
    needs: [build-darwin-framework]
    runs-on: macos-13
    steps:
    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
      with:
        repository: ${{ inputs.repo}}
        ref: ${{ inputs.ref }}
    - name: Download Build Artifacts (iOS x64)
      uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      with:
        name: Framework-${{ inputs.config }}-ios-macos-13-x64-${{ inputs.tls }}
        path: artifacts
    - name: Download Build Artifacts (iOS arm64)
      uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      with:
        name: Framework-${{ inputs.config }}-ios-macos-13-arm64-${{ inputs.tls }}
        path: artifacts
    - name: Download Build Artifacts (MacOS Universal)
      uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      with:
        name: Framework-${{ inputs.config }}-macos-macos-13-universal-${{ inputs.tls }}
        path: artifacts
    - name: Build XCFramework
      shell: pwsh
      run: scripts/package-darwin-xcframework.ps1 -Config ${{ inputs.config }} -Tls ${{ inputs.tls }}
    - name: Upload build artifacts
      uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02
      with:
        name: XCFramework-${{ inputs.config }}-${{ inputs.tls }}
        path: artifacts/frameworks
