name: Build WinUser

# The caller is responsible for making sure all options passed to this workflow are valid and compatible with each other.

on:
  workflow_call:
    inputs:
      ref:
        required: false
        default: ''
        type: string
      repo:
        required: false
        default: microsoft/msquic
        type: string
      config:
        required: false
        default: 'Release'
        type: string
        # options:
        #   - Debug
        #   - Release
      plat:
        required: false
        type: string
        default: 'windows'
        # options:
        #   - windows
        #   - uwp
        #   - winkernel
      os:
        required: false
        type: string
        default: 'windows-2022'
        # options:
        #   - windows-2022
        #   - windows-2025
      arch:
        required: false
        default: 'x64'
        type: string
        # options:
        #   - x86
        #   - x64
        #   - arm64
      tls:
        required: false
        default: 'schannel'
        type: string
        # options:
        #   - quictls
        #   - openssl 
        #   - schannel
      static:
        required: false
        default: ''
        type: string
      official:
        required: false
        default: ''
        type: string
      sanitize:
        required: false
        default: ''
        type: string
      build:
        required: false
        default: '' # Empty string means build all
        type: string

permissions: read-all

jobs:
  build-windows-reuse:
    if: inputs.plat == 'windows' || inputs.plat == 'uwp'
    name: Build
    runs-on: ${{ inputs.os }}
    steps:
    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
      with:
        repository: ${{ inputs.repo}}
        ref: ${{ inputs.ref }}
    - name: Install Perl
      uses: shogo82148/actions-setup-perl@22423f01bde48fb88785c007e3166fbbbd8e892a
      with:
        perl-version: '5.34'
    - name: Install NASM
      uses: ilammy/setup-nasm@72793074d3c8cdda771dba85f6deafe00623038b
    - name: Prepare Machine
      shell: pwsh
      run: scripts/prepare-machine.ps1 -ForBuild -Tls ${{ inputs.tls }}
    - name: Build For Test
      if: inputs.build == '-Test'
      shell: pwsh
      run: scripts/build.ps1 -Config ${{ inputs.config }} -Platform ${{ inputs.plat }} -Arch ${{ inputs.arch }} -Tls ${{ inputs.tls }} -DisablePerf -DynamicCRT ${{ inputs.sanitize }}
    - name: Build External Platform Test
      if: inputs.build == '-Test'
      shell: pwsh
      run: |
        cmake --install build\${{ inputs.plat }}\${{ inputs.arch }}_${{ inputs.tls }} --config ${{ inputs.config }}
        cmake src/platform/unittest/external -G "Visual Studio 17 2022" -A ${{ inputs.arch }} -B build_external "-DCMAKE_INSTALL_PREFIX:PATH=C:/Program Files/msquic"
        cmake --build build_external --config ${{ inputs.config }}
    - name: Build For Perf
      if: inputs.build == '-Perf'
      shell: pwsh
      run: scripts/build.ps1 -Config ${{ inputs.config }} -Platform ${{ inputs.plat }} -Arch ${{ inputs.arch }} -Tls ${{ inputs.tls }} -DisableTools -DisableTest ${{ inputs.sanitize }}
    - name: Build
      if: inputs.build == ''
      shell: pwsh
      run: scripts/build.ps1 -Config ${{ inputs.config }} -Platform ${{ inputs.plat }} -Arch ${{ inputs.arch }} -Tls ${{ inputs.tls }} ${{ inputs.sanitize }} ${{ inputs.static }} ${{ inputs.official }}
    - name: Filter Build Artifacts
      shell: pwsh
      run: |
        Remove-Item artifacts/bin/xdp -Recurse -Force -ErrorAction Ignore
        Remove-Item artifacts/corenet-ci-main -Recurse -Force -ErrorAction Ignore
        Remove-Item artifacts/xdp -Recurse -Force -ErrorAction Ignore
    - name: Upload build artifacts
      uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02
      with:
        name: ${{ inputs.config }}-${{ inputs.plat }}-${{ inputs.os }}-${{ inputs.arch }}-${{ inputs.tls }}${{ inputs.sanitize }}${{ inputs.static }}${{ inputs.official }}${{ inputs.build }}
        path: artifacts
