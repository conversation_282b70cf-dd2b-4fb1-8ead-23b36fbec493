name: DotNet Language Interop Tests

on:
  workflow_dispatch:
  push:
    branches:
    - main
    - release/*
  pull_request:
    branches:
    - main
    - release/*

concurrency:
  # Cancel any workflow currently in progress for the same PR.
  # Allow running concurrently with any other commits.
  group: dotnet-test-${{ github.event.pull_request.number || github.sha }}
  cancel-in-progress: true

permissions: read-all

jobs:
  build-darwin-frameworks:
    name: Build All Darwin Frameworks
    strategy:
      matrix:
        tls: ["quictls", "openssl"]
    uses: ./.github/workflows/build-reuse-darwin-framework.yml
    with:
      config: 'Debug'
      repo: ${{ github.repository }}
      tls: ${{ matrix.tls }}

  build-linux:
    name: Ubuntu
    needs: []
    strategy:
      fail-fast: false
      matrix:
        vec: [
          { plat: "linux", os: "ubuntu-22.04", arch: "x64", tls: "quictls" },
          { plat: "linux", os: "ubuntu-24.04", arch: "x64", tls: "quictls", xdp: "-UseXdp" },
          { plat: "linux", os: "ubuntu-22.04", arch: "x64", tls: "openssl" },
          { plat: "linux", os: "ubuntu-24.04", arch: "x64", tls: "openssl", xdp: "-UseXdp" },
        ]
    uses: ./.github/workflows/build-reuse-unix.yml
    with:
      config: 'Debug'
      plat: ${{ matrix.vec.plat }}
      os: ${{ matrix.vec.os }}
      arch: ${{ matrix.vec.arch }}
      tls: ${{ matrix.vec.tls }}
      xdp: ${{ matrix.vec.xdp }}
      repo: ${{ github.repository }}

  build-windows:
    name: Windows
    needs: []
    strategy:
      fail-fast: false
      matrix:
        vec: [
          { plat: "windows", os: "windows-2022", arch: "x64", tls: "quictls" },
          { plat: "windows", os: "windows-2022", arch: "x64", tls: "openssl" },
        ]
    uses: ./.github/workflows/build-reuse-win.yml
    with:
      config: 'Debug'
      plat: ${{ matrix.vec.plat }}
      os: ${{ matrix.vec.os }}
      arch: ${{ matrix.vec.arch }}
      tls: ${{ matrix.vec.tls }}
      repo: ${{ github.repository }}

  dotnet-test:
    name: DotNet Test
    needs: [build-windows, build-linux, build-darwin-frameworks]
    strategy:
      fail-fast: false
      matrix:
        vec: [
          { plat: "windows", os: "windows-2022", arch: "x64", tls: "quictls" },
          { plat: "linux", os: "ubuntu-22.04", arch: "x64", tls: "quictls" },
          { plat: "linux", os: "ubuntu-24.04", arch: "x64", tls: "quictls", xdp: "-UseXdp" },
          { plat: "linux", os: "ubuntu-22.04", arch: "x64", tls: "openssl" },
          { plat: "linux", os: "ubuntu-24.04", arch: "x64", tls: "openssl", xdp: "-UseXdp" },
          { plat: "macos", os: "macos-13", arch: "universal", tls: "quictls" },
          { plat: "macos", os: "macos-13", arch: "universal", tls: "openssl" },
        ]
    runs-on: ${{ matrix.vec.os }}
    steps:
    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
    - name: Prepare Machine
      shell: pwsh
      run: scripts/prepare-machine.ps1 -ForTest ${{ matrix.vec.xdp }}
    - name: Download Build Artifacts
      uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      with:
        name: Debug-${{ matrix.vec.plat }}-${{ matrix.vec.os }}-${{ matrix.vec.arch }}-${{ matrix.vec.tls }}${{ matrix.vec.xdp }}
        path: artifacts
    - uses: actions/setup-dotnet@67a3573c9a986a3f9c594539f4ab511d57bb3ce9
      with:
        dotnet-version: 6.0.x
    - name: Run Lang Interop
      shell: pwsh
      run: scripts/DotNetTest.ps1 -Config Debug -Arch ${{ matrix.vec.arch }} -Tls ${{ matrix.vec.tls }} -DomainName "google.com"
