name: Linux Packages

on:
  workflow_dispatch:
  push:
    branches:
    - main
    - release/*
  pull_request:
    branches:
    - main
    - release/*

concurrency:
  # Cancel any workflow currently in progress for the same PR.
  # Allow running concurrently with any other commits.
  group: package-linux-${{ github.event.pull_request.number || github.sha }}
  cancel-in-progress: true

permissions: read-all

jobs:
  build-packages:
    name: Generate Linux Packages
    needs: []
    strategy:
      fail-fast: false
      matrix:
        vec: [
          { config: "Release", os: "ubuntu-22.04", arch: "arm", tls: "quictls" },
          { config: "Release", os: "ubuntu-22.04", arch: "arm64", tls: "quictls" },
          { config: "Release", os: "ubuntu-22.04", arch: "x64", tls: "quictls" },
          { config: "Release", os: "ubuntu-24.04", arch: "arm", tls: "quictls", time64: "-Time64Distro" },
          { config: "Release", os: "ubuntu-24.04", arch: "arm64", tls: "quictls", time64: "-Time64Distro" },
          { config: "Release", os: "ubuntu-24.04", arch: "x64", tls: "quictls", xdp: "-UseXdp", time64: "-Time64Distro" },
        ]
    uses: ./.github/workflows/package-reuse-linux.yml
    with:
      config: ${{ matrix.vec.config }}
      os: ${{ matrix.vec.os }}
      arch: ${{ matrix.vec.arch }}
      tls: ${{ matrix.vec.tls }}
      xdp: ${{ matrix.vec.xdp }}
      time64: ${{ matrix.vec.time64 }}

  test-packages:
    name: Test Linux Packages
    needs: [build-packages]
    strategy:
      fail-fast: false
      matrix:
        vec: [
          { config: "Release", os: "ubuntu-22.04", arch: "x64", tls: "quictls" },
          { config: "Release", os: "ubuntu-24.04", arch: "x64", tls: "quictls", xdp: "-UseXdp" },
        ]
    runs-on: ${{ matrix.vec.os }}
    steps:
    - name: Download Package
      uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      with:
        name: ${{ matrix.vec.config }}-linux-${{ matrix.vec.os }}-${{ matrix.vec.arch }}-${{ matrix.vec.tls }}-UseSystemOpenSSLCrypto${{ matrix.vec.xdp }}
        path: artifacts
    - name: Download Build Artifacts
      uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      with:
        name: Package-${{ matrix.vec.config }}-linux-${{ matrix.vec.os }}-${{ matrix.vec.arch }}-${{ matrix.vec.tls }}-UseSystemOpenSSLCrypto${{ matrix.vec.xdp }}
        path: artifacts
    - name: Install Package
      run: |
        sudo apt-add-repository ppa:lttng/stable-2.13
        sudo apt-get update
        sudo apt-get install -y lttng-tools
        sudo find -name "*.deb" -exec sudo apt install -y ./{} \;
        rm artifacts/bin/linux/${{ matrix.vec.arch }}_${{ matrix.vec.config }}_${{ matrix.vec.tls }}/libmsquic.so*
        ls artifacts/bin/linux/${{ matrix.vec.arch }}_${{ matrix.vec.config }}_${{ matrix.vec.tls }}
    - name: Test
      run: |
        chmod +x artifacts/bin/linux/${{ matrix.vec.arch }}_${{ matrix.vec.config }}_${{ matrix.vec.tls }}/msquictest
        artifacts/bin/linux/${{ matrix.vec.arch }}_${{ matrix.vec.config }}_${{ matrix.vec.tls }}/msquictest --gtest_filter=ParameterValidation.ValidateApi
  test-packages-on-docker:
    name: Test Linux Packages
    needs: [build-packages]
    strategy:
      fail-fast: false
      matrix:
        vec: [
          # Ubuntu 24.04
          { friendlyName: "Ubuntu 24.04 x64", config: "Release", os: "ubuntu-24.04", arch: "x64",   tls: "quictls", image: "mcr.microsoft.com/dotnet/runtime:9.0-noble-amd64", xdp: "-UseXdp", dotnetVersion: "9.0" },
          { friendlyName: "Ubuntu 24.04 ARM32", config: "Release", os: "ubuntu-24.04", arch: "arm",   tls: "quictls", image: "mcr.microsoft.com/dotnet/runtime:9.0-noble-arm32v7", dotnetVersion: "9.0" },
          { friendlyName: "Ubuntu 24.04 ARM64", config: "Release", os: "ubuntu-24.04", arch: "arm64", tls: "quictls", image: "mcr.microsoft.com/dotnet/runtime:9.0-noble-arm64v8", dotnetVersion: "9.0" },
          # Debian 12
          { friendlyName: "Debian 12 x64", config: "Release", os: "ubuntu-22.04", arch: "x64",   tls: "quictls", image: "mcr.microsoft.com/dotnet/runtime:9.0-bookworm-slim-amd64", dotnetVersion: "9.0" },
          { friendlyName: "Debian 12 ARM32", config: "Release", os: "ubuntu-22.04", arch: "arm",   tls: "quictls", image: "mcr.microsoft.com/dotnet/runtime:9.0-bookworm-slim-arm32v7", dotnetVersion: "9.0" },
          { friendlyName: "Debian 12 ARM64", config: "Release", os: "ubuntu-22.04", arch: "arm64", tls: "quictls", image: "mcr.microsoft.com/dotnet/runtime:9.0-bookworm-slim-arm64v8", dotnetVersion: "9.0" },
          # Azure Linux 3.0
          { friendlyName: "AzureLinux 3.0 x64", config: "Release", os: "ubuntu-24.04", arch: "x64",   tls: "quictls", image: "mcr.microsoft.com/dotnet/runtime:9.0-azurelinux3.0-amd64", dotnetVersion: "9.0", xdp: "-UseXdp" },
          { friendlyName: "AzureLinux 3.0 ARM64", config: "Release", os: "ubuntu-24.04", arch: "arm64",   tls: "quictls", image: "mcr.microsoft.com/dotnet/runtime:9.0-azurelinux3.0-arm64v8", dotnetVersion: "9.0" },
          # Centos Stream 9
          { friendlyName: "CentOS Stream 9 x64", config: "Release", os: "ubuntu-22.04", arch: "x64",   tls: "quictls", image: "mcr.microsoft.com/dotnet-buildtools/prereqs:centos-stream9-helix", dotnetVersion: "9.0" },
          # Fedora 39 - 40
          { friendlyName: "Fedora 39 x64", config: "Release", os: "ubuntu-22.04", arch: "x64",   tls: "quictls", image: "mcr.microsoft.com/dotnet-buildtools/prereqs:fedora-39", dotnetVersion: "9.0" },
          { friendlyName: "Fedora 40 x64", config: "Release", os: "ubuntu-22.04", arch: "x64",   tls: "quictls", image: "mcr.microsoft.com/dotnet-buildtools/prereqs:fedora-40", dotnetVersion: "9.0" },
          # RHEL 8 - 9
          # { config: "Release", os: "ubuntu-24.04", arch: "x64", tls: "quictls", image: "redhat/ubi8-minimal:latest" },
          # { config: "Release", os: "ubuntu-24.04", arch: "x64", tls: "quictls", image: "redhat/ubi9-minimal:latest" },
        ]
    runs-on: ${{ matrix.vec.os }}
    steps:
    - name: Checkout Repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
    - name: Download Package
      uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      with:
        name: Package-${{ matrix.vec.config }}-linux-${{ matrix.vec.os }}-${{ matrix.vec.arch }}-${{ matrix.vec.tls }}-UseSystemOpenSSLCrypto${{ matrix.vec.xdp }}
        path: artifacts
    - name: Download Build Artifacts
      uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      with:
        name: ${{ matrix.vec.config }}-linux-${{ matrix.vec.os }}-${{ matrix.vec.arch }}-${{ matrix.vec.tls }}-UseSystemOpenSSLCrypto${{ matrix.vec.xdp }}
        path: artifacts
    - name: Set up QEMU
      uses: docker/setup-qemu-action@29109295f81e9208d7d86ff1c6c12d2833863392
    - name: Set up .NET 9.0
      uses: actions/setup-dotnet@67a3573c9a986a3f9c594539f4ab511d57bb3ce9
      with:
        dotnet-version: ${{ matrix.vec.dotnetVersion }}
    - name: Build .NET QUIC Test Project
      run: |
        pushd src/cs/QuicSimpleTest && dotnet build QuicHello.net${{ matrix.vec.dotnetVersion }}.csproj -a ${{ matrix.vec.arch }} -c ${{ matrix.vec.config }} -o artifacts/net${{ matrix.vec.dotnetVersion }} -f net${{ matrix.vec.dotnetVersion }} && popd
    - name: Docker Run
      run: |
        docker run -v $(pwd):/main ${{ matrix.vec.image }} /main/scripts/docker-script.sh ${{ matrix.vec.arch }} ${{ matrix.vec.config }} ${{ matrix.vec.tls }} ${{ matrix.vec.dotnetVersion }}

  Complete:
    name: Package Linux Complete
    if: always()
    needs: [build-packages, test-packages, test-packages-on-docker]
    runs-on: ubuntu-latest
    permissions: {} # No need for any permissions.
    steps:
    - name: Decide whether the needed jobs succeeded or failed
      uses: re-actors/alls-green@05ac9388f0aebcb5727afa17fcccfecd6f8ec5fe
      with:
        jobs: ${{ toJSON(needs) }}
