name: Alpine Linux Packages

on:
  workflow_dispatch:
  push:
    branches:
    - main
    - release/*
  pull_request:
    branches:
    - main
    - release/*

concurrency:
  # Cancel any workflow currently in progress for the same PR.
  # Allow running concurrently with any other commits.
  group: package-alpine-linux-${{ github.event.pull_request.number || github.sha }}
  cancel-in-progress: true

permissions: read-all

jobs:
  build-alpine-packages:
    name: Generate Alpine Packages
    needs: []
    strategy:
      fail-fast: false
      matrix:
        vec: [
          { friendlyName: "Alpine-3.20-x64", config: "Release", arch: "x64", tls: "quictls", image: "mcr.microsoft.com/dotnet/sdk:8.0-alpine3.20-amd64" },
        ]
    runs-on: ubuntu-latest
    steps:
    - name: Checkout Repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
    - name: Set up QEMU
      uses: docker/setup-qemu-action@29109295f81e9208d7d86ff1c6c12d2833863392
    - name: Generate APKBUILD
      shell: pwsh
      run: |
        ./scripts/generate-alpine-packaging-file.ps1 -ArchiveUri https://github.com/${{ github.repository }}/archive/${{ github.sha }}.tar.gz -SHA ${{ github.sha }}
        mkdir -p packages
    - name: Docker Run and Build Package
      run: |
        docker run \
                -v $(pwd)/packages:/artifacts \
                -v $(pwd):/msquic \
                ${{ matrix.vec.image }} /msquic/scripts/package-build.sh
    - name: Upload Package
      uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02
      with:
        name: ${{ matrix.vec.friendlyName }}-package
        path: packages/*.apk

  test-packages-on-docker:
    name: Test Linux Packages
    needs: [build-alpine-packages]
    strategy:
      fail-fast: false
      matrix:
        vec: [
          { friendlyName: "Alpine-3.20-x64", config: "Release", arch: "x64", tls: "quictls", image: "mcr.microsoft.com/dotnet/runtime:9.0-alpine3.20-amd64", dotnetVersion: "9.0" },
        ]
    runs-on: ubuntu-latest
    steps:
    - name: Checkout Repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
    - name: Download Package
      uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      with:
        name: ${{ matrix.vec.friendlyName }}-package
        path: artifacts
    - name: Set up QEMU
      uses: docker/setup-qemu-action@29109295f81e9208d7d86ff1c6c12d2833863392
    - name: Set up .NET 9.0
      uses: actions/setup-dotnet@67a3573c9a986a3f9c594539f4ab511d57bb3ce9
      with:
        dotnet-version: ${{ matrix.vec.dotnetVersion }}
    - name: Build .NET QUIC Test Project
      run: |
        pushd src/cs/QuicSimpleTest && dotnet build QuicHello.net${{ matrix.vec.dotnetVersion }}.csproj -a ${{ matrix.vec.arch }} -c ${{ matrix.vec.config }} -o artifacts/net${{ matrix.vec.dotnetVersion }} -f net${{ matrix.vec.dotnetVersion }} && popd
    - name: Docker Run
      run: |
        docker run -v $(pwd):/main ${{ matrix.vec.image }} /main/scripts/docker-script.sh ${{ matrix.vec.arch }} ${{ matrix.vec.config }} ${{ matrix.vec.tls }} ${{ matrix.vec.dotnetVersion }}
