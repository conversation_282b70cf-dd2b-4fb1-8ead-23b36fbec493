name: Stress

on:
  workflow_dispatch:
  push:
    branches:
    - main
    - release/*
  pull_request:
    branches:
    - main
    - release/*

concurrency:
  # Cancel any workflow currently in progress for the same PR.
  # Allow running concurrently with any other commits.
  group: stress-${{ github.event.pull_request.number || github.sha }}
  cancel-in-progress: true

permissions: read-all

jobs:
  build-windows:
    name: Build WinUser
    strategy:
      fail-fast: false
      matrix:
        vec: [
          { config: "Debug", plat: "windows", os: "windows-2022", arch: "x64", tls: "schannel", build: "-Test" },
          { config: "Debug", plat: "windows", os: "windows-2022", arch: "x64", tls: "schannel", sanitize: "-Sanitize", build: "-Test" },
          { config: "Debug", plat: "windows", os: "windows-2022", arch: "x64", tls: "quictls", build: "-Test" },
          { config: "Debug", plat: "windows", os: "windows-2022", arch: "x64", tls: "openssl", build: "-Test" },
        ]
    uses: ./.github/workflows/build-reuse-win.yml
    with:
      config: ${{ matrix.vec.config }}
      plat: ${{ matrix.vec.plat }}
      os: ${{ matrix.vec.os }}
      arch: ${{ matrix.vec.arch }}
      tls: ${{ matrix.vec.tls }}
      sanitize: ${{ matrix.vec.sanitize }}
      build: ${{ matrix.vec.build }}
      repo: ${{ github.repository }}

  build-unix:
    name: Build Unix
    strategy:
      fail-fast: false
      matrix:
        vec: [
          { config: "Debug", plat: "macos", os: "macos-13", arch: "x64", tls: "quictls", build: "-Test" },
          { config: "Debug", plat: "linux", os: "ubuntu-22.04", arch: "x64", tls: "quictls", sanitize: "-Sanitize", build: "-Test" },
          { config: "Debug", plat: "linux", os: "ubuntu-24.04", arch: "x64", tls: "quictls", sanitize: "-Sanitize", build: "-Test" },
          { config: "Debug", plat: "linux", os: "ubuntu-24.04", arch: "x64", tls: "quictls", build: "-Test", xdp: "-UseXdp" },
          { config: "Debug", plat: "macos", os: "macos-13", arch: "x64", tls: "openssl", build: "-Test" },
          { config: "Debug", plat: "linux", os: "ubuntu-22.04", arch: "x64", tls: "openssl", sanitize: "-Sanitize", build: "-Test" },
          { config: "Debug", plat: "linux", os: "ubuntu-24.04", arch: "x64", tls: "openssl", sanitize: "-Sanitize", build: "-Test" },
          { config: "Debug", plat: "linux", os: "ubuntu-24.04", arch: "x64", tls: "openssl", build: "-Test", xdp: "-UseXdp" },
       
        ]
    uses: ./.github/workflows/build-reuse-unix.yml
    with:
      config: ${{ matrix.vec.config }}
      plat: ${{ matrix.vec.plat }}
      os: ${{ matrix.vec.os }}
      arch: ${{ matrix.vec.arch }}
      tls: ${{ matrix.vec.tls }}
      systemcrypto: ${{ matrix.vec.systemcrypto }}
      sanitize: ${{ matrix.vec.sanitize }}
      build: ${{ matrix.vec.build }}
      xdp: ${{ matrix.vec.xdp }}
      repo: ${{ github.repository }}

  stress:
    name: Stress
    needs: [build-windows, build-unix]
    strategy:
      fail-fast: false
      matrix:
        vec: [
          { config: "Debug", plat: "linux", os: "ubuntu-22.04", arch: "x64", tls: "quictls", sanitize: "-Sanitize", build: "-Test"  },
          { config: "Debug", plat: "linux", os: "ubuntu-24.04", arch: "x64", tls: "quictls", sanitize: "-Sanitize", build: "-Test"  },
          { config: "Debug", plat: "linux", os: "ubuntu-24.04", arch: "x64", tls: "quictls", build: "-Test", xdp: "-UseXdp"  },
          { config: "Debug", plat: "macos", os: "macos-13", arch: "x64", tls: "quictls", build: "-Test" },
          { config: "Debug", plat: "linux", os: "ubuntu-22.04", arch: "x64", tls: "openssl", sanitize: "-Sanitize", build: "-Test"  },
          { config: "Debug", plat: "linux", os: "ubuntu-24.04", arch: "x64", tls: "openssl", sanitize: "-Sanitize", build: "-Test"  },
          { config: "Debug", plat: "linux", os: "ubuntu-24.04", arch: "x64", tls: "openssl", build: "-Test", xdp: "-UseXdp"  },
          { config: "Debug", plat: "macos", os: "macos-13", arch: "x64", tls: "openssl", build: "-Test" },
         
          { config: "Debug", plat: "windows", os: "windows-2022", arch: "x64", tls: "schannel", sanitize: "-Sanitize", build: "-Test" },
          { config: "Debug", plat: "windows", os: "windows-2022", arch: "x64", tls: "schannel", xdp: "-UseXdp", sanitize: "-Sanitize", build: "-Test" },
          { config: "Debug", plat: "windows", os: "windows-2022", arch: "x64", tls: "quictls", build: "-Test" },
          { config: "Debug", plat: "windows", os: "windows-2022", arch: "x64", tls: "quictls", xdp: "-UseXdp", build: "-Test" },
          { config: "Debug", plat: "windows", os: "WinServerPrerelease", arch: "x64", tls: "schannel", build: "-Test" },
        ]
    runs-on: ${{ matrix.vec.plat == 'windows' && matrix.vec.os == 'WinServerPrerelease' && fromJson('[''self-hosted'', ''1ES.Pool=1es-msquic-pool'', ''1ES.ImageOverride=WinServerPrerelease-LatestPwsh'']') || matrix.vec.os }}
    env:
      main-timeout: 3600000
      main-repeat: 100
      main-allocfail: 100
      pr-timeout: 600000
      pr-repeat: 20
      pr-allocfail: 100
    steps:
    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
    - uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      if: matrix.vec.plat == 'windows'
      with:
        name: ${{ matrix.vec.config }}-${{ matrix.vec.plat }}-windows-2022-${{ matrix.vec.arch }}-${{ matrix.vec.tls }}${{ matrix.vec.sanitize }}${{ matrix.vec.build }}
        path: artifacts
    - uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      if: matrix.vec.plat == 'linux' || matrix.vec.plat == 'macos'
      with:
        name: ${{ matrix.vec.config }}-${{ matrix.vec.plat }}-${{ matrix.vec.os }}-${{ matrix.vec.arch }}-${{ matrix.vec.tls }}${{ matrix.vec.sanitize }}${{ matrix.vec.xdp }}${{ matrix.vec.build }}
        path: artifacts
    - name: Fix permissions for Unix
      if: matrix.vec.plat == 'linux' || matrix.vec.plat == 'macos'
      run: |
        sudo chmod -R 777 artifacts
    - name: Prepare Machine
      run: scripts/prepare-machine.ps1 -Tls ${{ matrix.vec.tls }} -ForTest ${{ matrix.vec.xdp }}
      shell: pwsh
    - name: spinquic (PR)
      if: github.event_name == 'pull_request'
      timeout-minutes: 15
      shell: pwsh
      run: scripts/spin.ps1 -AZP -Config ${{ matrix.vec.config }} -Arch ${{ matrix.vec.arch }} -Tls ${{ matrix.vec.tls }} -Timeout ${{ env.pr-timeout }} -RepeatCount ${{ env.pr-repeat }} -AllocFail ${{ env.pr-allocfail }} ${{ matrix.vec.xdp }}
    - name: spinquic (Official)
      if: github.event_name != 'pull_request'
      timeout-minutes: 65
      shell: pwsh
      run: scripts/spin.ps1 -AZP -Config ${{ matrix.vec.config }} -Arch ${{ matrix.vec.arch }} -Tls ${{ matrix.vec.tls }} -Timeout ${{ env.main-timeout }} -RepeatCount ${{ env.main-repeat }} -AllocFail ${{ env.main-allocfail }} ${{ matrix.vec.xdp }}
    - name: Fix log permissions for Linux XDP
      if: failure() && matrix.vec.plat == 'linux' # (matrix.vec.plat == 'linux' && matrix.vec.xdp == '-UseXdp') doesn't work for some reason
      run: |
        sudo chmod -R 777 artifacts
    - name: Upload on Failure
      uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02
      if: failure()
      with:
        name: stress-${{ matrix.vec.config }}-${{ matrix.vec.plat }}-${{ matrix.vec.os }}-${{ matrix.vec.arch }}-${{ matrix.vec.tls }}${{ matrix.vec.xdp }}${{ matrix.vec.sanitize }}
        path: artifacts

  recvfuzz:
    name: recvfuzz
    needs: [build-windows, build-unix]
    strategy:
      fail-fast: false
      matrix:
        vec: [
          { config: "Debug", plat: "windows", os: "windows-2022", arch: "x64", tls: "schannel", sanitize: "-Sanitize", build: "-Test" },
          { config: "Debug", plat: "windows", os: "windows-2022", arch: "x64", tls: "schannel", xdp: "-UseXdp", sanitize: "-Sanitize", build: "-Test" },
          { config: "Debug", plat: "windows", os: "windows-2022", arch: "x64", tls: "quictls", build: "-Test" },
          { config: "Debug", plat: "windows", os: "windows-2022", arch: "x64", tls: "quictls", xdp: "-UseXdp", build: "-Test" },
          { config: "Debug", plat: "windows", os: "windows-2022", arch: "x64", tls: "openssl", build: "-Test" },
          { config: "Debug", plat: "windows", os: "windows-2022", arch: "x64", tls: "openssl", xdp: "-UseXdp", build: "-Test" },
          { config: "Debug", plat: "windows", os: "WinServerPrerelease", arch: "x64", tls: "schannel", build: "-Test" },
        ]
    runs-on: ${{ matrix.vec.plat == 'windows' && matrix.vec.os == 'WinServerPrerelease' && fromJson('[''self-hosted'', ''1ES.Pool=1es-msquic-pool'', ''1ES.ImageOverride=WinServerPrerelease-LatestPwsh'']') || matrix.vec.os }}
    env:
      main-timeout: 3600000
      pr-timeout: 600000
    steps:
    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
    - uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      if: matrix.vec.plat == 'windows'
      with:
        name: ${{ matrix.vec.config }}-${{ matrix.vec.plat }}-${{ matrix.vec.os == 'WinServerPrerelease' && 'windows-2022' || matrix.vec.os }}-${{ matrix.vec.arch }}-${{ matrix.vec.tls }}${{ matrix.vec.sanitize }}${{ matrix.vec.build }}
        path: artifacts
    - uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      if: matrix.vec.plat == 'linux' || matrix.vec.plat == 'macos'
      with:
        name: ${{ matrix.vec.config }}-${{ matrix.vec.plat }}-${{ matrix.vec.os }}-${{ matrix.vec.arch }}-${{ matrix.vec.tls }}${{ matrix.vec.sanitize }}${{ matrix.vec.build }}
        path: artifacts
    - name: Fix permissions for Unix
      if: matrix.vec.plat == 'linux' || matrix.vec.plat == 'macos'
      run: |
        sudo chmod -R 777 artifacts
    - name: Prepare Machine
      run: scripts/prepare-machine.ps1 -Tls ${{ matrix.vec.tls }} -ForTest ${{ matrix.vec.xdp }}
      shell: pwsh
    - name: recvfuzz (PR)
      if: github.event_name == 'pull_request'
      timeout-minutes: 15
      shell: pwsh
      run: scripts/recvfuzz.ps1 -AZP -Config ${{ matrix.vec.config }} -Arch ${{ matrix.vec.arch }} -Tls ${{ matrix.vec.tls }} -timeout ${{ env.pr-timeout }} ${{ matrix.vec.xdp }}
    - name: recvfuzz (Official)
      if: github.event_name != 'pull_request'
      timeout-minutes: 65
      shell: pwsh
      run: scripts/recvfuzz.ps1 -AZP -Config ${{ matrix.vec.config }} -Arch ${{ matrix.vec.arch }} -Tls ${{ matrix.vec.tls }} -timeout ${{ env.main-timeout }} ${{ matrix.vec.xdp }}
    - name: Upload on Failure
      uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02
      if: failure()
      with:
        name: recvfuzz-${{ matrix.vec.config }}-${{ matrix.vec.plat }}-${{ matrix.vec.os }}-${{ matrix.vec.arch }}-${{ matrix.vec.tls }}${{ matrix.vec.xdp }}${{ matrix.vec.sanitize }}
        path: artifacts

