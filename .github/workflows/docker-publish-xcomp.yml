name: Cross-Compile Docker

on:
  push:
    branches: [ main ]
    paths:
    - .github/workflows/docker-publish-xcomp.yml
    - .docker/ubuntu-22.04/*
    - .docker/ubuntu-24.04/*
  pull_request:
    branches: [ main ]
    paths:
    - .github/workflows/docker-publish-xcomp.yml
    - .docker/ubuntu-22.04/*
    - .docker/ubuntu-24.04/*

permissions: read-all

env:
  REGISTRY: ghcr.io
  # github.repository as <account>/<repo>
  IMAGE_NAME: ${{ github.repository }}/linux-build-xcomp

jobs:
  build:

    strategy:
      fail-fast: false
      matrix:
        version: ['22.04', '24.04']
        target: ['cross']

    name: Build
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683

      # Login against a Docker registry except on PR
      # https://github.com/docker/login-action
      - name: Log into registry ${{ env.REGISTRY }}
        if: github.event_name != 'pull_request'
        uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      # Extract metadata (tags, labels) for Docker
      # https://github.com/docker/metadata-action
      - name: Extract Docker metadata
        id: meta
        uses: docker/metadata-action@902fa8ec7d6ecbf8d84d538b9b233a880e428804
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}

      # Build and push Docker image with Buildx (don't push on PR)
      # https://github.com/docker/build-push-action
      - name: Build and push Docker image
        uses: docker/build-push-action@263435318d21b8e681c14492fe198d362a7d2c83
        with:
          context: .docker/ubuntu-${{ matrix.version }}
          file: .docker/ubuntu-${{ matrix.version }}/Dockerfile
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:ubuntu-${{ matrix.version }}-${{ matrix.target }}
          labels: ${{ steps.meta.outputs.labels }}
          target: ${{ matrix.target }}-build
