name: Cargo

on:
  workflow_dispatch:
  push:
    branches:
    - main
    - release/*
  pull_request:
    branches:
    - main
    - release/*

permissions:
  contents: read
  pull-requests: write

jobs:
  cargo:
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest, macos-latest-xlarge]
        vec: [
          { tls: "quictls", features: "" },
          { tls: "quictls", features: "--features static" },
          { tls: "quictls", features: "--features quictls" },
          { tls: "quictls", features: "--features quictls,static" },
          { tls: "openssl", features: "--features openssl" },
          { tls: "openssl", features: "--features openssl,static" },
          { tls: "quictls", features: "--features overwrite" }
        ]
    runs-on: ${{ matrix.os }}
    name: Cargo
    steps:
    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
    - name: Prepare Machine
      run: scripts/prepare-machine.ps1 -Tls ${{ matrix.vec.tls }} -ForBuild -InstallTestCertificates
      shell: pwsh
    - name: Install Perl
      if: runner.os == 'Windows'
      uses: shogo82148/actions-setup-perl@22423f01bde48fb88785c007e3166fbbbd8e892a
      with:
        perl-version: '5.34'
    - name: Install NASM
      if: runner.os == 'Windows'
      uses: ilammy/setup-nasm@72793074d3c8cdda771dba85f6deafe00623038b
    - name: Install Rust
      uses: dtolnay/rust-toolchain@b3b07ba8b418998c39fb20f53e8b695cdcc8de1b
      with:
        toolchain: 1.86.0
        components: rustfmt, clippy
    - name: Cargo fmt
      run: cargo fmt --all -- --check
    - name: Cargo clippy
      run: cargo clippy --all-targets ${{ matrix.vec.features }} -- -D warnings
    - name: Cargo build
      run: cargo build --all ${{ matrix.vec.features }}
    - name: Check all generated files with git
      id: bindings_diff
      shell: bash
      run: |
        if git diff --exit-code >> cargo_binding_update.patch; then
          echo "diff-found=false" >> $GITHUB_OUTPUT
        else
          echo "diff-found=true" >> $GITHUB_OUTPUT
          {
            echo 'diff-content<<@@@'
            echo "$(cat cargo_binding_update.patch)"
            echo '@@@'
          } >> $GITHUB_OUTPUT
          echo "$(cat cargo_binding_update.patch)"
        fi
    - name: Upload the patch file
      if: steps.bindings_diff.outputs.diff-found == 'true'
      id: upload_patch
      uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02
      with:
        name: cargo_binding_update_${{ matrix.os }}
        path: cargo_binding_update.patch
    - name: Post a comment on PR on mismatch
      if: |
        !github.event.pull_request.head.repo.fork &&
        steps.bindings_diff.outputs.diff-found == 'true'
      uses: peter-evans/create-or-update-comment@71345be0265236311c031f5c7866368bd1eff043
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        issue-number: ${{ github.event.number }}
        body: |
          ## ${{ github.workflow }} - ${{ matrix.os }}
          The rust bindings need to be updated. Please apply (`git apply`) this [patch](${{ steps.upload_patch.outputs.artifact-url }}):
          ```diff
          ${{ steps.bindings_diff.outputs.diff-content }}
          ```
        edit-mode: replace
    - name: Fail if generated files are not up to date
      if: steps.bindings_diff.outputs.diff-found == 'true'
      run: exit 1
    - name: Cargo test
      run: cargo test --all ${{ matrix.vec.features }}
    - name: Cargo Publish (dry run)
      run: cargo publish --dry-run --allow-dirty --no-verify
  # Test rust crate with preinstalled msquic lib.
  cargo-preinstall:
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest]
        features: ["--no-default-features --features find"]
    runs-on: ${{ matrix.os }}
    name: Cargo-Preinstall
    steps:
    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
    - name: Install Rust
      uses: dtolnay/rust-toolchain@b3b07ba8b418998c39fb20f53e8b695cdcc8de1b
      with:
        toolchain: 1.86.0
        components: rustfmt, clippy
    - name: Install msquic from apt
      run: |
        wget -q https://packages.microsoft.com/config/ubuntu/$(lsb_release -rs)/packages-microsoft-prod.deb;
        sudo dpkg -i packages-microsoft-prod.deb;
        sudo apt-get update;
        sudo apt-get install libmsquic;
        dpkg -L libmsquic;
    - name: Cargo fmt
      run: cargo fmt --all -- --check
    - name: Cargo clippy
      run: cargo clippy --all-targets ${{ matrix.features }} -- -D warnings
    - name: Cargo build
      run: cargo build --all ${{ matrix.features }}
    - name: Cargo test
      run: cargo test --all ${{ matrix.features }}
