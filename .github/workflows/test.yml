name: BVT

on:
  workflow_dispatch:
    inputs:
      ref:
        required: false
        default: ''
        type: string
      filter:
        required: false
        default: ''
        type: string
      log_level:
        required: true
        default: 'Full.Light'
        type: choice
        options:
        - 'Basic.Light'
        - 'Basic.Verbose'
        - 'Datapath.Light'
        - 'Datapath.Verbose'
        - 'Full.Light'
        - 'Full.Verbose'
        - 'Performance.Light'
        - 'Performance.Verbose'
        - 'RPS.Light'
        - 'RPS.Verbose'
        - 'SpinQuic.Light'
        - 'SpinQuicWarnings.Light'
        - 'Stacks.Light'
        - 'Stacks.Verbose'
  push:
    branches:
    - main
    - release/*
  pull_request:
    branches:
    - main
    - release/*

concurrency:
  # Cancel any workflow currently in progress for the same PR.
  # Allow running concurrently with any other commits.
  group: bvt-${{ github.event.pull_request.number || github.sha }}
  cancel-in-progress: true

permissions: read-all

jobs:
  build-windows-kernel:
    name: Build WinKernel
    strategy:
      fail-fast: false
      matrix:
        vec: [ # Note that GitHub windows-2025 doesn't have the necessary bits to build kernel drivers currently.
          { config: "Debug", plat: "winkernel", os: "windows-2022", arch: "x64", tls: "schannel", build: "-Test" },
          { config: "Release", plat: "winkernel", os: "windows-2022", arch: "x64", tls: "schannel", build: "-Test" }
        ]
    uses: ./.github/workflows/build-reuse-winkernel.yml
    with:
      config: ${{ matrix.vec.config }}
      plat: ${{ matrix.vec.plat }}
      os: ${{ matrix.vec.os }}
      arch: ${{ matrix.vec.arch }}
      tls: ${{ matrix.vec.tls }}
      build: ${{ matrix.vec.build }}
      ref: ${{ inputs.ref || '' }}
      repo: ${{ github.repository }}

  build-windows:
    name: Build WinUser
    strategy:
      fail-fast: false
      matrix:
        vec: [
          { config: "Debug", plat: "windows", os: "windows-2025", arch: "x64", tls: "schannel", build: "-Test" },
          { config: "Debug", plat: "windows", os: "windows-2025", arch: "x64", tls: "schannel", sanitize: "-Sanitize", build: "-Test" },
          { config: "Debug", plat: "windows", os: "windows-2025", arch: "x64", tls: "quictls", build: "-Test" },
          { config: "Debug", plat: "windows", os: "windows-2025", arch: "x64", tls: "openssl", build: "-Test" },
          { config: "Release", plat: "windows", os: "windows-2025", arch: "x64", tls: "schannel", build: "-Test" }
        ]
    uses: ./.github/workflows/build-reuse-win.yml
    with:
      config: ${{ matrix.vec.config }}
      plat: ${{ matrix.vec.plat }}
      os: ${{ matrix.vec.os }}
      arch: ${{ matrix.vec.arch }}
      tls: ${{ matrix.vec.tls }}
      sanitize: ${{ matrix.vec.sanitize }}
      build: ${{ matrix.vec.build }}
      ref: ${{ inputs.ref || '' }}
      repo: ${{ github.repository }}

  build-unix:
    name: Build Unix
    strategy:
      fail-fast: false
      matrix:
        vec: [
          { config: "Debug", plat: "linux", os: "ubuntu-22.04", arch: "x64", tls: "quictls", sanitize: "-Sanitize", build: "-Test" },
          { config: "Debug", plat: "linux", os: "ubuntu-22.04", arch: "x64", tls: "quictls", systemcrypto: "-UseSystemOpenSSLCrypto", sanitize: "-Sanitize", build: "-Test" },
          { config: "Release", plat: "linux", os: "ubuntu-22.04", arch: "x64", tls: "quictls", systemcrypto: "-UseSystemOpenSSLCrypto", build: "-Test" },
          { config: "Debug", plat: "linux", os: "ubuntu-24.04", arch: "x64", tls: "quictls", systemcrypto: "-UseSystemOpenSSLCrypto", sanitize: "-Sanitize", build: "-Test" },
          { config: "Release", plat: "linux", os: "ubuntu-24.04", arch: "x64", tls: "quictls", systemcrypto: "-UseSystemOpenSSLCrypto", build: "-Test" },
          { config: "Debug", plat: "linux", os: "ubuntu-24.04", arch: "x64", tls: "quictls", systemcrypto: "-UseSystemOpenSSLCrypto", build: "-Test", xdp: "-UseXdp" },
          { config: "Release", plat: "linux", os: "ubuntu-24.04", arch: "x64", tls: "quictls", systemcrypto: "-UseSystemOpenSSLCrypto", build: "-Test", xdp: "-UseXdp" },
          { config: "Debug", plat: "linux", os: "ubuntu-22.04", arch: "x64", tls: "openssl", sanitize: "-Sanitize", build: "-Test" },
          { config: "Debug", plat: "linux", os: "ubuntu-24.04", arch: "x64", tls: "openssl", sanitize: "-Sanitize", build: "-Test" },
        ]
    uses: ./.github/workflows/build-reuse-unix.yml
    with:
      config: ${{ matrix.vec.config }}
      plat: ${{ matrix.vec.plat }}
      os: ${{ matrix.vec.os }}
      arch: ${{ matrix.vec.arch }}
      tls: ${{ matrix.vec.tls }}
      systemcrypto: ${{ matrix.vec.systemcrypto }}
      sanitize: ${{ matrix.vec.sanitize }}
      build: ${{ matrix.vec.build }}
      xdp: ${{ matrix.vec.xdp }}
      ref: ${{ inputs.ref || '' }}
      repo: ${{ github.repository }}

  bvt:
    name: BVT
    needs: [build-windows, build-unix]
    strategy:
      fail-fast: false
      matrix:
        vec: [
          { config: "Debug", plat: "linux", os: "ubuntu-22.04", arch: "x64", tls: "quictls", sanitize: "-Sanitize", build: "-Test"  },
          { config: "Debug", plat: "linux", os: "ubuntu-22.04", arch: "x64", tls: "quictls", systemcrypto: "-UseSystemOpenSSLCrypto", sanitize: "-Sanitize", build: "-Test"  },
          { config: "Release", plat: "linux", os: "ubuntu-22.04", arch: "x64", tls: "quictls", systemcrypto: "-UseSystemOpenSSLCrypto", build: "-Test"  },
          { config: "Debug", plat: "linux", os: "ubuntu-24.04", arch: "x64", tls: "quictls", systemcrypto: "-UseSystemOpenSSLCrypto", sanitize: "-Sanitize", build: "-Test"  },
          { config: "Debug", plat: "linux", os: "ubuntu-24.04", arch: "x64", tls: "quictls", systemcrypto: "-UseSystemOpenSSLCrypto", build: "-Test", xdp: "-UseXdp"  },
          { config: "Release", plat: "linux", os: "ubuntu-24.04", arch: "x64", tls: "quictls", systemcrypto: "-UseSystemOpenSSLCrypto", build: "-Test"  },
          { config: "Release", plat: "linux", os: "ubuntu-24.04", arch: "x64", tls: "quictls", systemcrypto: "-UseSystemOpenSSLCrypto", build: "-Test", xdp: "-UseXdp"  },
          { config: "Debug", plat: "linux", os: "ubuntu-22.04", arch: "x64", tls: "openssl", sanitize: "-Sanitize", build: "-Test"  },
          { config: "Debug", plat: "windows", os: "windows-2019", arch: "x64", tls: "quictls", build: "-Test" },
          { config: "Debug", plat: "windows", os: "windows-2019", arch: "x64", tls: "openssl", build: "-Test" },
          { config: "Debug", plat: "windows", os: "windows-2022", arch: "x64", tls: "schannel", sanitize: "-Sanitize", build: "-Test" },
          { config: "Debug", plat: "windows", os: "windows-2022", arch: "x64", tls: "schannel", xdp: "-UseXdp", sanitize: "-Sanitize", build: "-Test" },
          { config: "Debug", plat: "windows", os: "windows-2022", arch: "x64", tls: "schannel", xdp: "-UseXdp", qtip: "-UseQtip", sanitize: "-Sanitize", build: "-Test" },
          { config: "Debug", plat: "windows", os: "windows-2022", arch: "x64", tls: "quictls", build: "-Test" },
          { config: "Debug", plat: "windows", os: "windows-2022", arch: "x64", tls: "quictls", xdp: "-UseXdp", build: "-Test" },
          { config: "Debug", plat: "windows", os: "windows-2022", arch: "x64", tls: "quictls", xdp: "-UseXdp", qtip: "-UseQtip", build: "-Test" },
          { config: "Debug", plat: "windows", os: "windows-2022", arch: "x64", tls: "openssl", build: "-Test" },
          { config: "Debug", plat: "windows", os: "windows-2022", arch: "x64", tls: "openssl", xdp: "-UseXdp", build: "-Test" },
          { config: "Debug", plat: "windows", os: "windows-2022", arch: "x64", tls: "openssl", xdp: "-UseXdp", qtip: "-UseQtip", build: "-Test" },
          { config: "Debug", plat: "windows", os: "windows-2025", arch: "x64", tls: "schannel", sanitize: "-Sanitize", build: "-Test" },
          { config: "Debug", plat: "windows", os: "windows-2025", arch: "x64", tls: "schannel", xdp: "-UseXdp", sanitize: "-Sanitize", build: "-Test" },
          { config: "Debug", plat: "windows", os: "windows-2025", arch: "x64", tls: "schannel", xdp: "-UseXdp", qtip: "-UseQtip", sanitize: "-Sanitize", build: "-Test" },
          { config: "Debug", plat: "windows", os: "windows-2025", arch: "x64", tls: "quictls", build: "-Test" },
          { config: "Debug", plat: "windows", os: "windows-2025", arch: "x64", tls: "quictls", xdp: "-UseXdp", build: "-Test" },
          { config: "Debug", plat: "windows", os: "windows-2025", arch: "x64", tls: "quictls", xdp: "-UseXdp", qtip: "-UseQtip", build: "-Test" },
          { config: "Debug", plat: "windows", os: "WinServerPrerelease", arch: "x64", tls: "schannel", build: "-Test" },
          { config: "Release", plat: "windows", os: "WinServerPrerelease", arch: "x64", tls: "schannel", build: "-Test" },
        ]
    runs-on: ${{ matrix.vec.plat == 'windows' && matrix.vec.os == 'WinServerPrerelease' && fromJson('[''self-hosted'', ''1ES.Pool=1es-msquic-pool'', ''1ES.ImageOverride=WinServerPrerelease-LatestPwsh'']') || matrix.vec.os }}
    steps:
    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
      with:
        fetch-depth: 0 # Don't need history.
        ref: ${{ inputs.ref || '' }}
    - name: Download Windows Build Artifacts
      uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      if: matrix.vec.plat == 'windows'
      with: # note we always use binaries built on windows-2025.
        name: ${{ matrix.vec.config }}-${{ matrix.vec.plat }}-windows-2025-${{ matrix.vec.arch }}-${{ matrix.vec.tls }}${{ matrix.vec.sanitize }}${{ matrix.vec.build }}
        path: artifacts
    - name: Download Linux Build Artifacts
      uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      if: matrix.vec.plat == 'linux'
      with:
        name: ${{ matrix.vec.config }}-${{ matrix.vec.plat }}-${{ matrix.vec.os }}-${{ matrix.vec.arch }}-${{ matrix.vec.tls }}${{ matrix.vec.systemcrypto }}${{ matrix.vec.sanitize }}${{ matrix.vec.xdp }}${{ matrix.vec.build }}
        path: artifacts
    - name: Fix permissions for Unix
      if: matrix.vec.plat == 'linux' || matrix.vec.plat == 'macos'
      run: |
        sudo chmod -R 777 artifacts
    - name: Prepare Machine
      run: scripts/prepare-machine.ps1 -Tls ${{ matrix.vec.tls }} -ForTest ${{ matrix.vec.xdp }}
      shell: pwsh
    - name: Install ETW Manifest
      if: matrix.vec.plat == 'windows'
      shell: pwsh
      run: |
        $MsQuicDll = ".\artifacts\bin\windows\${{ matrix.vec.arch }}_${{ matrix.vec.config }}_${{ matrix.vec.tls }}\msquic.dll"
        $ManifestPath = ".\src\manifest\MsQuicEtw.man"
        wevtutil.exe um $ManifestPath
        wevtutil.exe im $ManifestPath /rf:$($MsQuicDll) /mf:$($MsQuicDll)
    - name: Test
      if: matrix.vec.os == 'WinServerPrerelease'
      shell: pwsh
      timeout-minutes: 120
      run: scripts/test.ps1 -Config ${{ matrix.vec.config }} -Arch ${{ matrix.vec.arch }} -Tls ${{ matrix.vec.tls }} -GHA -LogProfile ${{ !inputs.log_level && 'Full.Light' || inputs.log_level }} -GenerateXmlResults ${{ matrix.vec.xdp }} ${{ matrix.vec.qtip }} ${{ inputs.filter && '-Filter' }} ${{ inputs.filter || '' }}
    - name: Test
      if: matrix.vec.os != 'WinServerPrerelease'
      shell: pwsh
      timeout-minutes: 120
      run: scripts/test.ps1 -Config ${{ matrix.vec.config }} -Arch ${{ matrix.vec.arch }} -Tls ${{ matrix.vec.tls }} -OsRunner ${{ matrix.vec.os }} -GHA -LogProfile ${{ !inputs.log_level && 'Full.Light' || inputs.log_level }} -GenerateXmlResults ${{ matrix.vec.xdp }} ${{ matrix.vec.qtip }} ${{ inputs.filter && '-Filter' }} ${{ inputs.filter || '' }}
    - name: Fix log permissions for Linux XDP
      if: failure() && matrix.vec.plat == 'linux' # (matrix.vec.plat == 'linux' && matrix.vec.xdp == '-UseXdp') doesn't work for some reason
      run: |
        sudo chmod -R 777 artifacts
    - name: Upload on Failure or Cancellation
      uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02
      if: failure() || cancelled()
      with:
        name: BVT-${{ matrix.vec.config }}-${{ matrix.vec.plat }}-${{ matrix.vec.os }}-${{ matrix.vec.arch }}-${{ matrix.vec.tls }}${{ matrix.vec.xdp }}${{ matrix.vec.qtip }}${{ matrix.vec.systemcrypto }}${{ matrix.vec.sanitize }}
        path: artifacts

  bvt-kernel:
    name: BVT Kernel
    needs: [build-windows, build-windows-kernel]
    strategy:
      fail-fast: false
      matrix:
        vec: [
          { config: "Debug", plat: "winkernel", os: "windows-2022", arch: "x64", tls: "schannel", build: "-Test" },
          { config: "Release", plat: "winkernel", os: "windows-2022", arch: "x64", tls: "schannel", build: "-Test" },
          { config: "Debug", plat: "winkernel", os: "WinServerPrerelease", arch: "x64", tls: "schannel", build: "-Test" },
          { config: "Release", plat: "winkernel", os: "WinServerPrerelease", arch: "x64", tls: "schannel", build: "-Test" },
        ]
    runs-on: ${{ matrix.vec.plat == 'winkernel' && matrix.vec.os == 'WinServerPrerelease' && fromJson('[''self-hosted'', ''1ES.Pool=1es-msquic-pool'', ''1ES.ImageOverride=WinServerPrerelease-LatestPwsh'']') || matrix.vec.os }}
    steps:
    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
      with:
        fetch-depth: 0 # Don't need history.
        ref: ${{ inputs.ref || '' }}
    - name: Download Kernel Build Artifacts
      uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      with: # note we always use kernel binaries built on windows-2022.
        name: ${{ matrix.vec.config }}-${{ matrix.vec.plat }}-windows-2022-${{ matrix.vec.arch }}-${{ matrix.vec.tls }}${{ matrix.vec.build }}
        path: artifacts
    - name: Download Usermode Build Artifacts
      uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      with: # note we always use binaries built on windows-2025.
        name: ${{ matrix.vec.config }}-windows-windows-2025-${{ matrix.vec.arch }}-${{ matrix.vec.tls }}${{ matrix.vec.build }}
        path: artifacts
    - name: Prepare Machine
      shell: pwsh
      run: scripts/prepare-machine.ps1 -ForTest -ForKernel
    - name: Install ETW Manifest
      shell: pwsh
      run: |
        $MsQuicDll = ".\artifacts\bin\windows\${{ matrix.vec.arch }}_${{ matrix.vec.config }}_${{ matrix.vec.tls }}\msquic.dll"
        $ManifestPath = ".\src\manifest\MsQuicEtw.man"
        wevtutil.exe um $ManifestPath
        wevtutil.exe im $ManifestPath /rf:$($MsQuicDll) /mf:$($MsQuicDll)
    - name: Test
      shell: pwsh
      timeout-minutes: 120
      run: scripts/test.ps1 -Config ${{ matrix.vec.config }} -Arch ${{ matrix.vec.arch }} -Tls ${{ matrix.vec.tls }} -OsRunner ${{ matrix.vec.os }} -GHA -LogProfile ${{ !inputs.log_level && 'Full.Light' || inputs.log_level }} -GenerateXmlResults -Kernel -Filter -*ValidateConfiguration:*ValidAlpnLengths:*ResumeRejection*:*ClientCertificate*:*LoadBalanced*
    - name: Upload on Failure or Cancellation
      uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02
      if: failure() || cancelled()
      with:
        name: BVT-Kernel-${{ matrix.vec.config }}-${{ matrix.vec.plat }}-${{ matrix.vec.os }}-${{ matrix.vec.arch }}-${{ matrix.vec.tls }}
        path: artifacts

  interop:
    name: Interop
    needs: [build-windows, build-unix]
    strategy:
      fail-fast: false
      matrix:
        vec: [
          { config: "Release", plat: "linux", os: "ubuntu-22.04", arch: "x64", tls: "quictls", build: "-Test" },
          { config: "Release", plat: "linux", os: "ubuntu-24.04", arch: "x64", tls: "quictls", build: "-Test" },
          { config: "Release", plat: "windows", os: "windows-2022", arch: "x64", tls: "schannel", build: "-Test" },
          { config: "Release", plat: "windows", os: "windows-2022", arch: "x64", tls: "schannel", build: "-Test", xdp: "-UseXdp" },
          { config: "Release", plat: "windows", os: "windows-2025", arch: "x64", tls: "schannel", build: "-Test" },
          { config: "Release", plat: "windows", os: "WinServerPrerelease", arch: "x64", tls: "schannel", build: "-Test" },
        ]
    runs-on: ${{ matrix.vec.plat == 'windows' && matrix.vec.os == 'WinServerPrerelease' && fromJson('[''self-hosted'', ''1ES.Pool=1es-msquic-pool'', ''1ES.ImageOverride=WinServerPrerelease-LatestPwsh'']') || matrix.vec.os }}
    steps:
    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
      with:
        fetch-depth: 0 # Don't need history.
        ref: ${{ inputs.ref || '' }}
    - name: Download Windows Build Artifacts
      uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      if: matrix.vec.plat == 'windows'
      with: # note we always use binaries built on windows-2025.
        name: ${{ matrix.vec.config }}-${{ matrix.vec.plat }}-windows-2025-${{ matrix.vec.arch }}-${{ matrix.vec.tls }}${{ matrix.vec.build }}
        path: artifacts
    - name: Download Linux Build Artifacts
      uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
      if: matrix.vec.plat == 'linux'
      with:
        name: ${{ matrix.vec.config }}-${{ matrix.vec.plat }}-${{ matrix.vec.os }}-${{ matrix.vec.arch }}-${{ matrix.vec.tls }}-UseSystemOpenSSLCrypto${{ matrix.vec.build }}
        path: artifacts
    - name: Fix permissions for Unix
      if: matrix.vec.plat == 'linux' || matrix.vec.plat == 'macos'
      run: |
        sudo chmod -R 777 artifacts
    - name: Prepare Machine
      run: scripts/prepare-machine.ps1 -ForTest ${{ matrix.vec.xdp }}
      shell: pwsh
    - name: Interop
      shell: pwsh
      run: scripts/interop.ps1 -Config ${{ matrix.vec.config }} -Arch ${{ matrix.vec.arch }} -Tls ${{ matrix.vec.tls }} -GHA -GenerateXmlResults
    - name: Upload results
      uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02
      if: failure() || cancelled()
      with:
        name: InteropWinlatest-${{ matrix.vec.config }}-${{ matrix.vec.plat }}-${{ matrix.vec.os }}-${{ matrix.vec.arch }}-${{ matrix.vec.tls }}${{ matrix.vec.xdp }}
        path: artifacts

  Complete:
    name: BVT Complete
    if: always()
    needs: [bvt, bvt-kernel, interop]
    runs-on: ubuntu-latest
    permissions: {} # No need for any permissions.
    steps:
    - name: Decide whether the needed jobs succeeded or failed
      uses: re-actors/alls-green@05ac9388f0aebcb5727afa17fcccfecd6f8ec5fe
      with:
        jobs: ${{ toJSON(needs) }}
