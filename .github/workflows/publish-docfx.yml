
name: Deploy DocFx

on:
  workflow_dispatch:
  # Runs on pushes targeting the default branch
  push:
    branches: ["main"]
    paths: ["docs/**", ".github/workflows/publish-docfx.yml"]

  pull_request:
    branches: ["main"]
    paths: ["docs/**", ".github/workflows/publish-docfx.yml"]

permissions: read-all

concurrency:
  # Cancel any workflow currently in progress for the same PR.
  # Allow running concurrently with any other commits.
  group: deploy-docfx-${{ github.event.pull_request.number || github.sha }}
  cancel-in-progress: true

jobs:
  publish-docs:
    permissions:
      contents: write
      pages: write
      id-token: write
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
    - name: Dotnet Setup
      uses: actions/setup-dotnet@67a3573c9a986a3f9c594539f4ab511d57bb3ce9
      with:
        dotnet-version: 8.x

    - run: dotnet tool update -g docfx
    - run: chmod +x ./scripts/prepare-docfx.ps1
    - run: ./scripts/prepare-docfx.ps1
      shell: pwsh
    - run: chmod +x ./scripts/generate-docfx-yml.ps1
    - run: ./scripts/generate-docfx-yml.ps1 ./docs
      shell: pwsh
    - run: chmod +x ./scripts/docfx-replace-url.ps1
      shell: pwsh
    - run: ./scripts/docfx-replace-url.ps1
      shell: pwsh
    - run: Set-Item -Path Env:DOCFX_SOURCE_BRANCH_NAME  -Value 'main'
      shell: pwsh
    - run: docfx docfx.json
    - run: chmod +x ./scripts/update-docfx-site.ps1
    - name: Commit Changes
      if: (github.event_name == 'push' && github.ref == 'refs/heads/main') || (github.event_name == 'workflow_dispatch')
      run: ./scripts/update-docfx-site.ps1
      shell: pwsh
