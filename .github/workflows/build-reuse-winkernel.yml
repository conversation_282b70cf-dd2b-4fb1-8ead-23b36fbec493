name: Build WinKernel

# The caller is responsible for making sure all options passed to this workflow are valid and compatible with each other.

on:
  workflow_call:
    inputs:
      ref:
        required: false
        default: ''
        type: string
      repo:
        required: false
        default: microsoft/msquic
        type: string
      config:
        required: false
        default: 'Release'
        type: string
        # options:
        #   - Debug
        #   - Release
      plat:
        required: false
        type: string
        default: 'winkernel'
        # options:
        #   - winkernel
      os:
        required: false
        type: string
        default: 'windows-2022'
        # options:
        #   - windows-2022
      arch:
        required: false
        default: 'x64'
        type: string
        # options:
        #   - x86
        #   - x64
        #   - arm64
      tls:
        required: false
        default: 'schannel'
        type: string
        # options:
        #   - quictls
        #   - openssl 
        #   - schannel
      build:
        required: false
        default: '' # Empty string means build all
        type: string

permissions: read-all

jobs:
  build-windows-kernel-reuse:
    name: Build
    runs-on: ${{ inputs.os }}
    steps:
    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
      with:
        repository: ${{ inputs.repo}}
        ref: ${{ inputs.ref }}
    - name: Prepare Machine
      shell: pwsh
      run: scripts/prepare-machine.ps1 -ForBuild -ForKernel
    - name: Add msbuild to PATH
      uses: microsoft/setup-msbuild@6fb02220983dee41ce7ae257b6f4d8f9bf5ed4ce
    - name: Nuget Restore
      shell: pwsh
      run: msbuild msquic.kernel.sln -t:restore /p:RestorePackagesConfig=true /p:Configuration=${{ inputs.config }} /p:Platform=${{ inputs.arch }}
    - name: Build
      if: inputs.build == '-Test'
      shell: pwsh
      run: msbuild msquic.kernel.sln /m /p:Configuration=${{ inputs.config }} /p:Platform=${{ inputs.arch }} /p:QUIC_VER_SUFFIX=-official /p:QUIC_VER_GIT_HASH=${{ github.sha }}
    - name: Build
      if: inputs.build == ''
      shell: pwsh
      run: msbuild msquic.kernel.sln /m /p:Configuration=${{ inputs.config }} /p:Platform=${{ inputs.arch }} /p:QUIC_VER_SUFFIX=-official
    - name: Sign Kernel
      shell: pwsh
      run: scripts/sign.ps1 -Config ${{ inputs.config }} -Arch ${{ inputs.arch }} -Tls ${{ inputs.tls }}
    - name: Filter Build Artifacts
      shell: pwsh
      run: |
        Remove-Item artifacts/bin/xdp -Recurse -Force -ErrorAction Ignore
        Remove-Item artifacts/corenet-ci-main -Recurse -Force -ErrorAction Ignore
        Remove-Item artifacts/xdp -Recurse -Force -ErrorAction Ignore
    - name: Upload build artifacts
      uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02
      with:
        name: ${{ inputs.config }}-${{ inputs.plat }}-${{ inputs.os }}-${{ inputs.arch }}-${{ inputs.tls }}${{ inputs.build }}
        path: artifacts
