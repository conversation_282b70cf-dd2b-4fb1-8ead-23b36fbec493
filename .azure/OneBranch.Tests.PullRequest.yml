trigger: none # https://aka.ms/obpipelines/triggers

resources:
  pipelines:
  - pipeline: onebranch   # Name of the pipeline resource
    source: msquic-PullRequest # Name of the pipeline referenced by the pipeline resource
    trigger: true

name: 0.$(Date:yyyy).$(Date:MM).$(DayOfMonth).$(Rev:rr).0

variables:
  DisableDockerDetector: true

stages:
- stage: package
  displayName: Package
  dependsOn: []
  jobs:
  - template: ./obtemplates/build-distribution.yml

#
# Build Verification Tests
#

- stage: test_bvt
  displayName: BVT
  dependsOn: []
  jobs:
  - template: ./obtemplates/run-bvt.yml
    parameters:
      pool: 1es-msquic-pool-internal
      image: WinServerPrerelease-LatestPwsh
      platform: windows
      tls: schannel
      logProfile: Full.Light

#
# Build Verification Tests (Kernel Mode)
#

- stage: test_bvt_kernel
  displayName: BVT Kernel
  dependsOn: []
  jobs:
  - template: ./obtemplates/run-bvt.yml
    parameters:
      pool: 1es-msquic-pool-internal
      image: WinServerPrerelease-LatestPwsh
      platform: windows
      tls: schannel
      logProfile: Full.Light
      extraArgs: -Kernel -Filter -*NthAllocFail*
      kernel: true

#
# Windows Release BVTs
#

- stage: test_bvt_windows_release
  displayName: BVT Windows Release
  dependsOn: []
  jobs:
  - template: ./obtemplates/run-bvt.yml
    parameters:
      pool: 1es-msquic-pool-internal
      image: WinServerPrerelease-LatestPwsh
      platform: windows
      tls: schannel
      logProfile: Full.Light
      config: Release
      extraArgs: -SkipUnitTests

#
# Windows Release Kernel BVTs
#

- stage: test_bvt_winkernel_release
  displayName: BVT Windows Kernel Release
  dependsOn: []
  jobs:
  - template: ./obtemplates/run-bvt.yml
    parameters:
      pool: 1es-msquic-pool-internal
      image: WinServerPrerelease-LatestPwsh
      platform: windows
      tls: schannel
      logProfile: Full.Light
      config: Release
      kernel: true
      extraArgs: -Kernel
