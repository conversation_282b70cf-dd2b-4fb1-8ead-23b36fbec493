#
# Uploads SIGNED packages generated by Official build pipeline
#
trigger: none # https://aka.ms/obpipelines/triggers

resources:
  pipelines:
  - pipeline: onebranch     # Name of the pipeline resource
    source: msquic-Official # Name of the pipeline referenced by the pipeline resource
    trigger: true

name: 0.$(Date:yyyy).$(Date:MM).$(DayOfMonth).$(Rev:rr).0

variables:
  DisableDockerDetector: true

parameters:
- name: debug # debug mode will not actually upload and publish packages
  type: boolean
  default: false

- name: kernel
  type: object
  default:
  # built on ubuntu 22.04, quictls
  - name: kernel5_15
    type: object
    default:
    - name: RPM
      destinations:
        - microsoft-fedora36-prod-yum   # 36 5.17
        - microsoft-fedora37-prod-yum   # 37 6.0
        - microsoft-fedora38-prod-yum   # 38 6.2
        - microsoft-fedora39-prod-yum   # 39 6.5
        - microsoft-rhel9.0-prod-yum    # 9.0 5.14
    - name: DEB
      destinations:
        - microsoft-ubuntu-jammy-prod-apt    # 22.04 5.15
        - microsoft-ubuntu-kinetic-prod-apt  # 22.10 5.19
        - microsoft-ubuntu-lunar-prod-apt    # 23.04 6.2
        - microsoft-ubuntu-mantic-prod-apt   # 23.10 6.5
        - microsoft-debian-bookworm-prod-apt # 12    6.1
  # built on ubuntu 24.04 quictls XDP
  - name: kernel6_8
    type: object
    default:
    - name: RPM
      destinations:
        - microsoft-fedora40-prod-yum           # 40 6.8
        - microsoft-fedora41-prod-yum           # 41 6.11
    - name: DEB
      destinations:
        - microsoft-ubuntu-noble-prod-apt       # 24.04 6.8
    - name: CBL
      destinations:
        - azurelinux-3.0-prod-ms-oss-x86_64-yum  # 3.0 6.6
        - azurelinux-3.0-prod-ms-oss-aarch64-yum # 3.0 6.6

stages:
- stage: UploadPackage_stage
  condition: or(startsWith(variables['Build.SourceBranch'], 'refs/tags/'), eq(variables['Build.Reason'], 'Manual'))
  jobs:
  - ${{ each kernel in parameters.kernel }}:
    - ${{ each repo_type in kernel.default }}:
      - job: UploadPackage_${{ kernel.name }}_${{ repo_type.name }}
        displayName: Upload ${{ kernel.name }} based ${{ repo_type.name }} packages to repos
        timeoutInMinutes: 120
        workspace:
          clean: all
        pool:
          vmImage: 'ubuntu-latest'
        variables:
        - group: MsQuicAADApp
        steps:
          - task: DownloadPipelineArtifact@2
            inputs:
              source: specific
              project: $(resources.pipeline.onebranch.projectID)
              pipeline: $(resources.pipeline.onebranch.pipelineID)
              preferTriggeringPipeline: true
              runVersion: specific
              runId: $(resources.pipeline.onebranch.runID)
              artifact: drop_package_linux_distribution_${{ kernel.name }}
              path: $(Build.SourcesDirectory)/artifacts/signed/${{ kernel.name }}
          - task: DownloadSecureFile@1
            name:  pmcv4cert
            displayName: 'Download cert for PMC v4'
            inputs:
              secureFile: 'auth.pem'
          - ${{ if eq(repo_type.name, 'CBL' )}}:
            - ${{ each repo in repo_type.destinations }}:
              - script: bash scripts/upload-linux-packages.sh -i $(PMCv4ClientId) -c $(pmcv4cert.secureFilePath) -f $(Build.SourcesDirectory)/artifacts/signed/${{ kernel.name }}/cbl -r ${{ repo }} -n "*.rpm"
                condition: eq(${{ parameters.debug }}, false)
                displayName: ${{ repo }}
                continueOnError: true
          - ${{ else }}:
            - ${{ each repo in repo_type.destinations }}:
              - script: bash scripts/upload-linux-packages.sh -i $(PMCv4ClientId) -c $(pmcv4cert.secureFilePath) -f $(Build.SourcesDirectory)/artifacts/signed/${{ kernel.name }}/gen -r ${{ repo }} -n "*.${{ lower(repo_type.name) }}"
                condition: eq(${{ parameters.debug }}, false)
                displayName: ${{ repo }}
                continueOnError: true
