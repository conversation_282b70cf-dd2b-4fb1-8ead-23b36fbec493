{"tool": "Credential Scanner", "suppressions": [{"file": ["submodules/quictls/apps/ca-key.pem", "submodules/quictls/apps/client.pem", "submodules/quictls/apps/dsa-ca.pem", "submodules/quictls/apps/dsa-pca.pem", "submodules/quictls/apps/pca-key.pem", "submodules/quictls/apps/privkey.pem", "submodules/quictls/apps/rsa8192.pem", "submodules/quictls/apps/s1024key.pem", "submodules/quictls/apps/s512-key.pem", "submodules/quictls/apps/server.pem", "submodules/quictls/apps/server2.pem", "submodules/quictls/fuzz/oids.txt", "submodules/quictls/test/insta.priv.pem", "submodules/quictls/test/shibboleth.pfx", "submodules/quictls/test/testdsa.pem", "submodules/quictls/test/testec-p112r1.pem", "submodules/quictls/test/testec-p256.pem", "submodules/quictls/test/tested25519.pem", "submodules/quictls/test/tested448.pem", "submodules/quictls/test/testrsa.pem", "submodules/quictls/test/testrsa2048.pem", "submodules/quictls/test/testrsapss.pem", "submodules/quictls/test/testrsapssmandatory.pem", "submodules/quictls/test/testrsa_withattrs.pem", "submodules/quictls/demos/bio/server-ec.pem", "submodules/quictls/demos/bio/server.pem", "submodules/quictls/demos/cms/cakey.pem", "submodules/quictls/demos/cms/signer.pem", "submodules/quictls/demos/cms/signer2.pem", "submodules/quictls/demos/smime/cakey.pem", "submodules/quictls/demos/smime/signer.pem", "submodules/quictls/demos/smime/signer2.pem", "submodules/quictls/demos/sslecho/key.pem", "submodules/quictls/test/certs/alt1-key.pem", "submodules/quictls/test/certs/alt2-key.pem", "submodules/quictls/test/certs/alt3-key.pem", "submodules/quictls/test/certs/bad-othername-namec-key.pem", "submodules/quictls/test/certs/bad-pc3-key.pem", "submodules/quictls/test/certs/bad-pc4-key.pem", "submodules/quictls/test/certs/bad-pc6-key.pem", "submodules/quictls/test/certs/bad.key", "submodules/quictls/test/certs/badalt1-key.pem", "submodules/quictls/test/certs/badalt10-key.pem", "submodules/quictls/test/certs/badalt2-key.pem", "submodules/quictls/test/certs/badalt3-key.pem", "submodules/quictls/test/certs/badalt4-key.pem", "submodules/quictls/test/certs/badalt5-key.pem", "submodules/quictls/test/certs/badalt6-key.pem", "submodules/quictls/test/certs/badalt7-key.pem", "submodules/quictls/test/certs/badalt8-key.pem", "submodules/quictls/test/certs/badalt9-key.pem", "submodules/quictls/test/certs/badcn1-key.pem", "submodules/quictls/test/certs/ca-key-768.pem", "submodules/quictls/test/certs/ca-key-ec-named.pem", "submodules/quictls/test/certs/ca-key.pem", "submodules/quictls/test/certs/ca-key2.pem", "submodules/quictls/test/certs/ca-pss-key.pem", "submodules/quictls/test/certs/cert-key-cert.pem", "submodules/quictls/test/certs/client-ed25519-key.pem", "submodules/quictls/test/certs/client-ed448-key.pem", "submodules/quictls/test/certs/cross-key.pem", "submodules/quictls/test/certs/ct-server-key.pem", "submodules/quictls/test/certs/ec_privkey_with_chain.pem", "submodules/quictls/test/certs/ee-ecdsa-key.pem", "submodules/quictls/test/certs/ee-key-1024.pem", "submodules/quictls/test/certs/ee-key-3072.pem", "submodules/quictls/test/certs/ee-key-4096.pem", "submodules/quictls/test/certs/ee-key-768.pem", "submodules/quictls/test/certs/ee-key-8192.pem", "submodules/quictls/test/certs/ee-key-ec-named-explicit.pem", "submodules/quictls/test/certs/ee-key-ec-named-named.pem", "submodules/quictls/test/certs/ee-key.pem", "submodules/quictls/test/certs/embeddedSCTs1-key.pem", "submodules/quictls/test/certs/embeddedSCTs1_issuer-key.pem", "submodules/quictls/test/certs/goodcn1-key.pem", "submodules/quictls/test/certs/goodcn2-key.pem", "submodules/quictls/test/certs/interCA.key", "submodules/quictls/test/certs/leaf-encrypted.key", "submodules/quictls/test/certs/leaf.key", "submodules/quictls/test/certs/ncca-key.pem", "submodules/quictls/test/certs/ncca1-key.pem", "submodules/quictls/test/certs/ncca2-key.pem", "submodules/quictls/test/certs/ncca3-key.pem", "submodules/quictls/test/certs/nccaothername-key.pem", "submodules/quictls/test/certs/p256-server-key.pem", "submodules/quictls/test/certs/p384-root-key.pem", "submodules/quictls/test/certs/p384-server-key.pem", "submodules/quictls/test/certs/pc1-key.pem", "submodules/quictls/test/certs/pc2-key.pem", "submodules/quictls/test/certs/pc5-key.pem", "submodules/quictls/test/certs/root-ed25519.privkey.pem", "submodules/quictls/test/certs/root-ed448-key.pem", "submodules/quictls/test/certs/root-key-768.pem", "submodules/quictls/test/certs/root-key.pem", "submodules/quictls/test/certs/root-key2.pem", "submodules/quictls/test/certs/rootCA.key", "submodules/quictls/test/certs/rootkey.pem", "submodules/quictls/test/certs/server-cecdsa-key.pem", "submodules/quictls/test/certs/server-dsa-key.pem", "submodules/quictls/test/certs/server-ecdsa-brainpoolP256r1-key.pem", "submodules/quictls/test/certs/server-ecdsa-key.pem", "submodules/quictls/test/certs/server-ed25519-key.pem", "submodules/quictls/test/certs/server-ed448-key.pem", "submodules/quictls/test/certs/server-pss-key.pem", "submodules/quictls/test/certs/server-pss-restrict-key.pem", "submodules/quictls/test/certs/serverkey.pem", "submodules/quictls/test/certs/sm2-root.key", "submodules/quictls/test/certs/sm2.key", "submodules/quictls/test/certs/subinterCA.key", "submodules/quictls/test/certs/timing-key.pem", "submodules/quictls/test/certs/v3-certs-RC2.p12", "submodules/quictls/test/certs/v3-certs-TDES.p12", "submodules/quictls/test/certs/wrongkey.pem", "submodules/quictls/test/certs/x509-check-key.pem", "submodules/quictls/test/smime-certs/smdh.pem", "submodules/quictls/test/smime-certs/smdsa1.pem", "submodules/quictls/test/smime-certs/smdsa2.pem", "submodules/quictls/test/smime-certs/smdsa3.pem", "submodules/quictls/test/smime-certs/smec1.pem", "submodules/quictls/test/smime-certs/smec2.pem", "submodules/quictls/test/smime-certs/smec3.pem", "submodules/quictls/test/smime-certs/smroot.pem", "submodules/quictls/test/smime-certs/smrsa1.pem", "submodules/quictls/test/smime-certs/smrsa2.pem", "submodules/quictls/test/smime-certs/smrsa3.pem", "submodules/quictls/test/smime-certs/smrsa1024.pem", "submodules/quictls/demos/certs/apps/ckey.pem", "submodules/quictls/demos/certs/apps/intkey.pem", "submodules/quictls/demos/certs/apps/rootkey.pem", "submodules/quictls/demos/certs/apps/skey.pem", "submodules/quictls/demos/certs/apps/skey2.pem", "submodules/quictls/test/recipes/04-test_pem_reading_data/beermug.pem", "submodules/quictls/test/recipes/04-test_pem_reading_data/dsa-1023line.pem", "submodules/quictls/test/recipes/04-test_pem_reading_data/dsa-1024line.pem", "submodules/quictls/test/recipes/04-test_pem_reading_data/dsa-1025line.pem", "submodules/quictls/test/recipes/04-test_pem_reading_data/dsa-255line.pem", "submodules/quictls/test/recipes/04-test_pem_reading_data/dsa-256line.pem", "submodules/quictls/test/recipes/04-test_pem_reading_data/dsa-257line.pem", "submodules/quictls/test/recipes/04-test_pem_reading_data/dsa-blankline.pem", "submodules/quictls/test/recipes/04-test_pem_reading_data/dsa-comment.pem", "submodules/quictls/test/recipes/04-test_pem_reading_data/dsa-corruptiv.pem", "submodules/quictls/test/recipes/04-test_pem_reading_data/dsa-earlypad.pem", "submodules/quictls/test/recipes/04-test_pem_reading_data/dsa-extrapad.pem", "submodules/quictls/test/recipes/04-test_pem_reading_data/dsa-infixwhitespace.pem", "submodules/quictls/test/recipes/04-test_pem_reading_data/dsa-junk.pem", "submodules/quictls/test/recipes/04-test_pem_reading_data/dsa-longline.pem", "submodules/quictls/test/recipes/04-test_pem_reading_data/dsa-misalignedpad.pem", "submodules/quictls/test/recipes/04-test_pem_reading_data/dsa-onecolumn.pem", "submodules/quictls/test/recipes/04-test_pem_reading_data/dsa-oneline.pem", "submodules/quictls/test/recipes/04-test_pem_reading_data/dsa-onelineheader.pem", "submodules/quictls/test/recipes/04-test_pem_reading_data/dsa-shortandlongline.pem", "submodules/quictls/test/recipes/04-test_pem_reading_data/dsa-shortline.pem", "submodules/quictls/test/recipes/04-test_pem_reading_data/dsa-threecolumn.pem", "submodules/quictls/test/recipes/04-test_pem_reading_data/dsa-trailingwhitespace.pem", "submodules/quictls/test/recipes/04-test_pem_reading_data/dsa.pem", "submodules/quictls/test/recipes/04-test_pem_reading_data/key.pem", "submodules/quictls/test/recipes/04-test_pem_read_depr_data/dsaprivatekey.pem", "submodules/quictls/test/recipes/04-test_pem_read_depr_data/rsaprivatekey.pem", "submodules/quictls/test/recipes/15-test_mp_rsa_data/rsamplcm.pem", "submodules/quictls/test/recipes/15-test_rsapss_data/negativesaltlen.pem", "submodules/quictls/test/recipes/30-test_evp_data/evppbe_pbkdf2.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppbe_pkcs12.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppbe_pkcs12.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_brainpool.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_brainpool.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_brainpool.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_brainpool.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_brainpool.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_brainpool.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_brainpool.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_brainpool.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_brainpool.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_brainpool.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_brainpool.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_brainpool.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_brainpool.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_brainpool.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_brainpool.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_brainpool.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_brainpool.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_brainpool.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_brainpool.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_brainpool.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_brainpool.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_brainpool.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_brainpool.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_brainpool.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_brainpool.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_brainpool.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_brainpool.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_brainpool.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_brainpool.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_brainpool.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_brainpool.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_brainpool.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_dh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_dh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_dsa.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecc.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdh.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecdsa.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecx.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecx.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecx.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecx.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecx.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ecx.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ffdhe.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ffdhe.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ffdhe.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ffdhe.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ffdhe.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_ffdhe.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_kas.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_mismatch.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_rsa.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_rsa_common.txt", "submodules/quictls/test/recipes/30-test_evp_data/evppkey_sm2.txt", "submodules/quictls/test/recipes/30-test_evp_pkey_provided/DSA.priv.pem", "submodules/quictls/test/recipes/30-test_evp_pkey_provided/EC.priv.pem", "submodules/quictls/test/recipes/30-test_evp_pkey_provided/ED25519.priv.pem", "submodules/quictls/test/recipes/30-test_evp_pkey_provided/ED448.priv.pem", "submodules/quictls/test/recipes/30-test_evp_pkey_provided/RSA.priv.pem", "submodules/quictls/test/recipes/30-test_evp_pkey_provided/X25519.priv.pem", "submodules/quictls/test/recipes/30-test_evp_pkey_provided/X448.priv.pem", "submodules/quictls/test/recipes/65-test_cmp_client_data/client.key", "submodules/quictls/test/recipes/65-test_cmp_client_data/server.key", "submodules/quictls/test/recipes/65-test_cmp_msg_data/new.key", "submodules/quictls/test/recipes/65-test_cmp_protect_data/server.pem", "submodules/quictls/test/recipes/65-test_cmp_vfy_data/insta.priv.pem", "submodules/quictls/test/recipes/65-test_cmp_vfy_data/server.key", "submodules/quictls/test/recipes/80-test_ca_data/revoked.key", "submodules/quictls/test/recipes/80-test_ocsp_data/key.pem", "submodules/quictls/test/recipes/90-test_gost_data/server-key2001.pem", "submodules/quictls/test/recipes/90-test_gost_data/server-key2012.pem", "submodules/quictls/test/recipes/90-test_sslapi_data/dhparams.pem", "submodules/quictls/test/recipes/90-test_store_cases_data/garbage-pkcs12.p12", "submodules/quictls/test/recipes/90-test_store_data/rsa-key-2432.pem", "submodules/quictls/test/recipes/90-test_store_data/testrsa.pvk", "submodules/quictls/test/recipes/90-test_threads_data/rsakey.pem", "submodules/quictls/test/recipes/91-test_pkey_check_data/dhpkey.pem", "submodules/quictls/test/recipes/80-test_cmp_http_data/Mock/new.key", "submodules/quictls/test/recipes/80-test_cmp_http_data/Mock/new_pass_12345.key", "submodules/quictls/test/recipes/80-test_cmp_http_data/Mock/server.key", "submodules/quictls/test/recipes/80-test_cmp_http_data/Mock/signer.key", "submodules/quictls/test/recipes/80-test_cmp_http_data/Mock/signer.p12"], "_justification": "external modules"}, {"file": ["src/core/cubic.c", "src/core/cubic.c", "src/core/cubic.c", "src/core/cubic.c", "docs/TSG.md", "docs/TSG.md", "docs/TSG.md", "src/core/congestion_control.c", "src/core/congestion_control.c", "src/platform/crypt_bcrypt.c", "src/platform/tls_schannel.c", "src/platform/tls_schannel.c", "src/platform/tls_schannel.c", "src/platform/tls_schannel.c", "src/generated/linux/crypt_bcrypt.c.clog.h", "src/generated/linux/tls_schannel.c.clog.h", "src/generated/linux/tls_schannel.c.clog.h"], "_justification": "false positives"}]}