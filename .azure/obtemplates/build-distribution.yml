jobs:
- job: distribution
  displayName: Distribution
  variables:
  - name: runCodesignValidationInjection
    value: false
  - name: skipComponentGovernanceDetection
    value: true
  pool:
    vmImage: ubuntu-latest

  steps:
  - template: ./download-artifacts.yml
    parameters:
      platform: linux
      kernel: kernel5_15
      config: Release
      tls: quictls
  - template: ./download-artifacts.yml
    parameters:
      platform: linux
      kernel: kernel5_15
      config: Debug
      tls: quictls

  - template: ./download-artifacts.yml
    parameters:
      platform: linux
      kernel: kernel6_8
      config: Release
      tls: quictls
  - template: ./download-artifacts.yml
    parameters:
      platform: linux
      kernel: kernel6_8
      config: Debug
      tls: quictls

  - template: ./download-artifacts.yml
    parameters:
      platform: windows
      config: Release
      tls: quictls
  - template: ./download-artifacts.yml
    parameters:
      platform: windows
      config: Debug
      tls: quictls

  - template: ./download-artifacts.yml
    parameters:
      platform: windows
      config: Release
      tls: schannel
  - template: ./download-artifacts.yml
    parameters:
      platform: windows
      config: Debug
      tls: schannel

  - template: ./download-artifacts.yml
    parameters:
      platform: uwp
      config: Release
      tls: schannel
  - template: ./download-artifacts.yml
    parameters:
      platform: uwp
      config: Debug
      tls: schannel

  - template: ./download-artifacts.yml
    parameters:
      platform: gamecore_console
      config: Release
      tls: schannel
  - template: ./download-artifacts.yml
    parameters:
      platform: gamecore_console
      config: Debug
      tls: schannel

  - task: PowerShell@2
    displayName: Prepare Build Machine
    inputs:
      pwsh: true
      filePath: scripts/prepare-machine.ps1
      arguments: -ForBuild

  - task: PowerShell@2
    displayName: Distribution
    inputs:
      pwsh: false
      filePath: scripts/package-distribution.ps1

  - task: CopyFiles@2
    displayName: Move Distribution
    inputs:
      sourceFolder: artifacts/dist
      targetFolder: $(Build.ArtifactStagingDirectory)

  - task: PublishBuildArtifacts@1
    displayName: Upload Distribution
    inputs:
      artifactName: distribution
      pathToPublish: $(Build.ArtifactStagingDirectory)
      parallel: true
