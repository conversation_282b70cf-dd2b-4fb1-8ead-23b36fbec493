#
# Run this OBP will generate a docker image and push it to isolated ACR hosted by Onebranch.
#
# The pipeline also runs regularly per the schedule defined below. Since we do not frequently
# change the Dockerfile, most scheduled runs should be no-op, i.e. nothing is really pushed to
# the Onebranch isolated ACR. Only when the base image gets updated, the updated subsequent layers
# will be rebuilt and pushed.
#

trigger:
  batch: true
  branches:
    include:
    - main
  paths:
    include:
    - .docker/windows/*

schedules:
- cron: '0 0 * * Sat' # Every Saturday
  displayName: Weekly docker build
  branches:
    include:
    - main

parameters: # parameters are shown up in ADO UI in a build queue time
- name: 'debug'
  displayName: 'Enable debug output'
  type: boolean
  default: false

variables:
  CDP_DEFINITION_BUILD_COUNT: $[counter('', 0)] # needed for onebranch.pipeline.version task https://aka.ms/obpipelines/versioning
  system.debug: ${{ parameters.debug }}
  ENABLE_PRS_DELAYSIGN: 1
  DisableDockerDetector: true
  ROOT: $(Build.SourcesDirectory)
  REPOROOT: $(Build.SourcesDirectory)
  OUTPUTROOT: $(REPOROOT)\out
  NUGET_XMLDOC_MODE: none
  WindowsContainerImage: 'onebranch.azurecr.io/windows/ltsc2022/vse2022:latest'

resources:
  repositories:
    - repository: templates
      type: git
      name: OneBranch.Pipelines/GovernedTemplates
      ref: refs/heads/main

extends:
  template: v2/OneBranch.Official.CrossPlat.yml@templates # https://aka.ms/obpipelines/templates
  parameters:
    cloudvault: # https://aka.ms/obpipelines/cloudvault
      enabled: false
    globalSdl: # https://aka.ms/obpipelines/sdl
      sbom:
        validate: false
      tsa:
        enabled: false # onebranch publish all sdl results to TSA. If TSA is disabled all SDL tools will forced into 'break' build mode.
      credscan:
        suppressionsFile: $(Build.SourcesDirectory)\.azure\CredScanSuppressions.json
      suppression:
        suppressionFile: $(Build.SourcesDirectory)\.azure\openssl.gdnsuppress
    featureFlags:
      skipPoliciesValidation: true
      WindowsHostVersion: '1ESWindows2022'

    stages:
      - stage: docker
        jobs:
        - job: download_external_libs
          displayName: Download external libs
          pool:
            type: windows  # read more about custom job types at https://aka.ms/obpipelines/yaml/jobs
          variables: # More settings at https://aka.ms/obpipelines/yaml/jobs
            ob_outputDirectory: '$(Build.SourcesDirectory)/out' # this directory is uploaded to pipeline artifacts, reddog and cloudvault
          steps:
            - task: DownloadSecureFile@1
              name:  xgameplatformlib
              displayName: 'Download xgameplatform.lib'
              inputs:
                secureFile: 'xgameplatform.lib'
            - script: |
                mkdir -p $(Build.SourcesDirectory)\out
                copy $(xgameplatformlib.secureFilePath) $(Build.SourcesDirectory)\out
        - job: windowsContainers # build windows images
          dependsOn: download_external_libs
          displayName: Build docker image for windows
          pool:
            type: docker
            os: windows
          variables:
            ob_git_checkout: true
          steps:
            - task: DownloadPipelineArtifact@2
              displayName: '🔒 Download artifacts'
              inputs:
                targetPath: $(Build.SourcesDirectory)\dst\.docker\windows
                artifact: drop_docker_download_external_libs
            - task: onebranch.pipeline.imagebuildinfo@1
              inputs:
                repositoryName: msquicbuild
                dockerFileRelPath: .docker\windows\Dockerfile
                dockerFileContextPath: .docker\windows
                enable_network: true
                build_tag: '$(Build.BuildNumber)'
                enable_isolated_acr_push: true
                enable_service_tree_acr_path: true
