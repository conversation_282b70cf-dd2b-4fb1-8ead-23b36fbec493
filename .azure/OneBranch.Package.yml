trigger: none # https://aka.ms/obpipelines/triggers

resources:
  pipelines:
  - pipeline: onebranch   # Name of the pipeline resource
    source: msquic-Official # Name of the pipeline referenced by the pipeline resource
    branch: main
  repositories:
    - repository: templates
      type: git
      name: OneBranch.Pipelines/GovernedTemplates
      ref: refs/heads/main

parameters:
- name: branch
  type: string
  displayName: Branch to PR to
  default: official/rs_onecore_liof1_stack
- name: title
  type: string
  displayName: PR Title
  default: 'Automated: Ingest MsQuic'
# - name: msrc
#   type: string
#   displayName: MSRC Number
#   default: ''

name: 0.$(Date:yyyy).$(Date:MM).$(DayOfMonth).$(Rev:rr).0

variables:
  DisableDockerDetector: true
  WindowsContainerImage: 'onebranch.azurecr.io/windows/ltsc2022/vse2022:latest'

extends:
  template: v2/Microsoft.Official.yml@templates # https://aka.ms/obpipelines/templates
  parameters:
    platform:
      name: 'windows_undocked' # windows undocked
    globalSdl: # https://aka.ms/obpipelines/sdl
      tsa:
        enabled: false # onebranch publish all sdl results to TSA. If TSA is disabled all SDL tools will forced into 'break' build mode.
      credscan:
        suppressionsFile: $(Build.SourcesDirectory)\.azure\CredScanSuppressions.json
      suppression:
        suppressionFile: $(Build.SourcesDirectory)\.azure\openssl.gdnsuppress
    featureFlags:
      WindowsHostVersion: '1ESWindows2022'

    stages:
    - stage: prepare
      pool:
        type: windows
      displayName: Prepare VPack
      dependsOn: []
      jobs:
      - template: .azure/obtemplates/prepare-vpacks.yml@self

    - stage: build_vpack
      displayName: Build VPack
      dependsOn: [prepare]
      jobs:
      - job: package
        pool:
          type: windows
        strategy:
          matrix:
            amd64fre:
              platform: amd64fre
            amd64chk:
              platform: amd64chk
            arm64fre:
              platform: arm64fre
            arm64chk:
              platform: arm64chk
            armfre:
              platform: armfre
            armchk:
              platform: armchk
            chpefre:
              platform: chpefre
            chpechk:
              platform: chpechk
            x86fre:
              platform: x86fre
            x86chk:
              platform: x86chk
        variables:
          runCodesignValidationInjection: false
          group: VPackDeploymentKeys
          ob_outputDirectory: $(Build.SourcesDirectory)/artifacts/package/$(platform)
          ob_artifactSuffix: _$(platform)
          ob_sdl_codeSignValidation_excludes: -|**\*.sys;-|**\*.dll;-|**\*.exe
          ob_createvpack_enabled: true
          ob_createvpack_packagename: msquic.$(platform)
          ob_createvpack_owneralias: quicdev
          ob_createvpack_description: msquic.$(Build.SourceBranchName)
          ob_createvpack_versionAs: string
          ob_createvpack_version: 2.6.0-$(Build.BuildId)
        steps:
        - task: DownloadPipelineArtifact@2
          inputs:
            artifact: drop_prepare_preparepackage
            path: $(Build.SourcesDirectory)/artifacts/package

    - stage: push_git
      displayName: Push to Git
      dependsOn:
      - build_vpack
      jobs:
      - template: .azure/obtemplates/git-submit.yml@self
        parameters:
          branch: ${{ parameters.branch }}
          title: ${{ parameters.title }}
          #msrc: ${{ parameters.msrc }}
