#################################################################################
#                         OneBranch Pipelines - Official                        #
# This pipeline was created by EasyStart from a sample located at:              #
#   https://aka.ms/obpipelines/easystart/samples                                #
# Documentation:  https://aka.ms/obpipelines                                    #
# Yaml Schema:    https://aka.ms/obpipelines/yaml/schema                        #
# Retail Tasks:   https://aka.ms/obpipelines/tasks                              #
# Support:        https://aka.ms/onebranchsup                                   #
#################################################################################

trigger:
  batch: true
  branches:
    include:
    - main
    - release/*
    - msrc/*
  tags:
    include:
    - v*

parameters: # parameters are shown up in ADO UI in a build queue time
- name: 'debug'
  displayName: 'Enable debug output'
  type: boolean
  default: false
- name: 'WindowsContainerImage2DockerTag'
  displayName: 'WindowsContainerImage2 DockerTag'
  type: string
  default: '20240713.1' # if initializing docker takes too long, grab a newer docker image from Docker.Official pipeline.

variables:
  CDP_DEFINITION_BUILD_COUNT: $[counter('', 0)] # needed for onebranch.pipeline.version task https://aka.ms/obpipelines/versioning
  system.debug: ${{ parameters.debug }}
  ENABLE_PRS_DELAYSIGN: 1
  DisableDockerDetector: true
  ROOT: $(Build.SourcesDirectory)
  REPOROOT: $(Build.SourcesDirectory)
  OUTPUTROOT: $(REPOROOT)\out
  NUGET_XMLDOC_MODE: none
  ONEBRANCH_AME_ACR_LOGIN: onebranch.azurecr.io, cdpxb7b51c2f738e43e48f7605d9a8e5f6d700.azurecr.io

resources:
  repositories:
    - repository: templates
      type: git
      name: OneBranch.Pipelines/GovernedTemplates
      ref: refs/heads/main

extends:
  template: v2/OneBranch.Official.CrossPlat.yml@templates # https://aka.ms/obpipelines/templates
  parameters:
    cloudvault: # https://aka.ms/obpipelines/cloudvault
      enabled: false
    globalSdl: # https://aka.ms/obpipelines/sdl
      sbom:
        validate: false
      tsa:
        enabled: false # onebranch publish all sdl results to TSA. If TSA is disabled all SDL tools will forced into 'break' build mode.
      credscan:
        suppressionsFile: $(Build.SourcesDirectory)\.azure\CredScanSuppressions.json
      binskim:
        break: true # always break the build on binskim issues in addition to TSA upload
      policheck:
        break: true # always break the build on policheck issues. You can disable it by setting to 'false'
      suppression:
        suppressionFile: $(Build.SourcesDirectory)\.azure\openssl.gdnsuppress
    featureFlags:
      linuxEsrpSigningPreview: true
      WindowsHostVersion: '1ESWindows2022'
    containers:
      - container: windows_build_container
        image: 'onebranch.azurecr.io/windows/ltsc2022/vse2022:latest'
        type: Windows
      - container: windows_build_container2
        image: 'cdpxb7b51c2f738e43e48f7605d9a8e5f6d700.azurecr.io/b7b51c2f-738e-43e4-8f76-05d9a8e5f6d7/official/msquicbuild:${{ parameters.WindowsContainerImage2DockerTag }}'
        type: Windows
      - container: linux_build_container # Default container
        image: 'ghcr.io/microsoft/msquic/linux-build-xcomp:ubuntu-20.04-cross'
        type: Linux
      - container: kernel5_15_cross
        image: 'ghcr.io/microsoft/msquic/linux-build-xcomp:ubuntu-22.04-cross'
        type: Linux
      - container: kernel6_8_cross
        image: 'ghcr.io/microsoft/msquic/linux-build-xcomp:ubuntu-24.04-cross'
        type: Linux

    stages:
    - stage: build_winkernel
      displayName: Build Windows Kernel
      dependsOn: []
      jobs:
      - template: .azure/obtemplates/build-winkernel.yml@self
        parameters:
          config: Release
      - template: .azure/obtemplates/build-winkernel.yml@self
        parameters:
          config: Debug

    - stage: build_windows
      displayName: Build Windows
      dependsOn: []
      jobs:
      - template: .azure/obtemplates/build-winuser.yml@self
        parameters:
          config: Release
          tls: schannel
      - template: .azure/obtemplates/build-winuser.yml@self
        parameters:
          config: Debug
          tls: schannel
      - template: .azure/obtemplates/build-winuser.yml@self
        parameters:
          config: Release
          tls: quictls
      - template: .azure/obtemplates/build-winuser.yml@self
        parameters:
          config: Debug
          tls: quictls

    - stage: build_gamecore_console
      displayName: Build Gamecore
      dependsOn: []
      jobs:
      - template: .azure/obtemplates/build-winuser.yml@self
        parameters:
          config: Release
          tls: schannel
          platform: gamecore_console
      - template: .azure/obtemplates/build-winuser.yml@self
        parameters:
          config: Debug
          tls: schannel
          platform: gamecore_console

    - stage: build_uwp
      displayName: Build Windows UWP
      dependsOn: []
      jobs:
      - template: .azure/obtemplates/build-winuser.yml@self
        parameters:
          config: Release
          tls: schannel
          platform: uwp
      - template: .azure/obtemplates/build-winuser.yml@self
        parameters:
          config: Debug
          tls: schannel
          platform: uwp

    - stage: build_linux
      displayName: Build Linux
      dependsOn: []
      jobs:
      - template: .azure/obtemplates/build-linux.yml@self
        parameters:
          config: Release
          kernel: "kernel5_15"
          tls: quictls
      - template: .azure/obtemplates/build-linux.yml@self
        parameters:
          config: Debug
          kernel: "kernel5_15"
          tls: quictls
      - template: .azure/obtemplates/build-linux.yml@self
        parameters:
          config: Release
          kernel: "kernel6_8"
          tls: quictls
          xdp: "-UseXdp"
      - template: .azure/obtemplates/build-linux.yml@self
        parameters:
          config: Debug
          kernel: "kernel6_8"
          tls: quictls
          xdp: "-UseXdp"

    - stage: package_linux
      displayName: Package Linux
      dependsOn:
      - build_linux
      jobs:
      - template: .azure/obtemplates/build-linux-packages.yml@self
        parameters:
          sign: true
          kernel: "kernel5_15"
      - template: .azure/obtemplates/build-linux-packages.yml@self
        parameters:
          sign: true
          kernel: "kernel6_8"

    - stage: package_windows
      displayName: Package Windows
      dependsOn:
      - build_windows
      - build_uwp
      jobs:
      - template: .azure/obtemplates/build-nuget.yml@self
      # Create official Nuget packages for tags
        ${{ if startsWith(variables['Build.SourceBranch'], 'refs/tags/') }}:
          parameters:
            release: true
