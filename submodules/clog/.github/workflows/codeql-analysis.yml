---
name: "CodeQL"

on:
  push:
    branches:
      - main
      - release/*
  pull_request:
    # The branches below must be a subset of the branches above
    branches:
      - main
      - release/*
  schedule:
    - cron: '0 17 * * 1'

jobs:
  analyze:
    name: Analyze
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v2
        with:
          fetch-depth: 2
          submodules: 'recursive'

      - name: Setup .NET
        uses: actions/setup-dotnet@v1
        with:
          dotnet-version: 6.0.x

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v1
        with:
          languages: cpp #,csharp
          config-file: ./.github/codeql/codeql-config.yml

      # Build
      - run: |
         mkdir build
         cd build
         cmake ..
         cmake --build .
         cd ..
         find

      # Analyze this build.
      - run: |
         cd build
         cmake --build .

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v1
