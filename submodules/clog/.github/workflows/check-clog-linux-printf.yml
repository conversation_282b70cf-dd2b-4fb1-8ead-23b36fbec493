name: LINUX_TEST_BUILD_PRINTF

on:
  push:
    branches:
    - main
    - release/*
  pull_request:
    branches:
    - main
    - release/*

jobs:
  validate:
    name: <PERSON><PERSON><PERSON>(Linux)
    runs-on: ubuntu-latest
    steps:
    - name: Checkout repository
      uses: actions/checkout@v2

    - name: Setup .NET
      uses: actions/setup-dotnet@v1
      with:
        dotnet-version: 6.0.x

    - name: Build CLOG and run Tests
      run: ./runTests_stdio.ps1
      working-directory: ./examples
      shell: pwsh
