
# At a high level, whats the problem?
* Backend infra, analysis tooling, and trace API are tightly coupled - lots of duplicated work
* Product/OS needs often mandates bit encoder - which leads to the cart(encoder) leading the horse (API/tools)
* Once you've chosen API, you also chosen tooling
* Windows has at least 3 similar bit encoders; Linux has even more


# Tracing on Windows
|               |   OS    | Capability         | printf() style | Manifest Location |
| ------------- | ------- | ------------------ |--------------- | ----------------- | 
| Manifested    | Windows | DiagTrack/OneTrace | No             | External/OnBox    |
| Tracelogging  | Windows | DiagTrack/OneTrace | No             | Internal          |
| WPP           | Windows | -                  | Yes            | External/PDB      | 
| Lttng         | Linux   | -                  | No             | Internal          |
| printf()      | All     | -                  | No             | Internal          |



# CLog Interesting Decisions (ones with lots of thought)
* Decouples the API surface from the bit encoder
* API choice should introduce *zero* instructions beyond what the target encoder requires (eliminating classes/inheritance)
* Durable ID's are always required to support advanced tooling tooling
* Events should be created with zero friction to the programmer; manifests are friction
* Debugging strings shouldnt ship, unless you want to
* Tools should be usable, no matter if the bit encoders change
* Existing tooling should continue to work, so long as the tooling and bit encoders are compatible

# Universal CLog Workflow
![](WorkFlow.png)

# What goes into config file?
* Macro Name
* Encoding types (8bit, 16bit, 32bit, 64bit, 128bit, guid, string types, and binary)
* Decoding instructions - including option to create your own decoder in C#
![](ConfigSnipit.png)
    
# What goes into source file?
* #include "file.cpp.clog.h" <-- generated by CLOG
* link CLOG lib (not always required)
* event fully described within the printf() macro
![](sample%20source.png)


# Demo Work flow
1. at build time, your c/cpp file will be read and processed using a config file.  clog.exe produces .c file and .h file that contains the OS specific code for your OS
2. Using your favorite OS collection mechanism, collect traces using the appropriate tools
3. For debugging, convert your recorded traces into something human readable using one of the clog2text_{windows,lttng,etc} tools
