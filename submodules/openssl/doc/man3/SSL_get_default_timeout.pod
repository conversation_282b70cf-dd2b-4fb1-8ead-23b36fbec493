=pod

=head1 NAME

SSL_get_default_timeout - get default session timeout value

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 long SSL_get_default_timeout(const SSL *ssl);

=head1 DESCRIPTION

SSL_get_default_timeout() returns the default timeout value assigned to
SSL_SESSION objects negotiated for the protocol valid for B<ssl>.

=head1 NOTES

Whenever a new session is negotiated, it is assigned a timeout value,
after which it will not be accepted for session reuse. If the timeout
value was not explicitly set using
L<SSL_CTX_set_timeout(3)>, the hardcoded default
timeout for the protocol will be used.

SSL_get_default_timeout() return this hardcoded value, which is 300 seconds
for all currently supported protocols.

=head1 RETURN VALUES

See description.

=head1 SEE ALSO

L<ssl(7)>,
L<SSL_CTX_set_session_cache_mode(3)>,
L<SSL_SESSION_get_time(3)>,
L<SSL_CTX_flush_sessions(3)>,
L<SSL_get_default_timeout(3)>

=head1 COPYRIGHT

Copyright 2001-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
