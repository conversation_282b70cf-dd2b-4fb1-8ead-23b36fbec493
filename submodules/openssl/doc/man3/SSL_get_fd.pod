=pod

=head1 NAME

SSL_get_fd, SSL_get_rfd, SSL_get_wfd - get file descriptor linked to an SSL object

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 int SSL_get_fd(const SSL *ssl);
 int SSL_get_rfd(const SSL *ssl);
 int SSL_get_wfd(const SSL *ssl);

=head1 DESCRIPTION

SSL_get_fd() returns the file descriptor which is linked to B<ssl>.
SSL_get_rfd() and SSL_get_wfd() return the file descriptors for the
read or the write channel, which can be different. If the read and the
write channel are different, SSL_get_fd() will return the file descriptor
of the read channel.

=head1 RETURN VALUES

The following return values can occur:

=over 4

=item -1

The operation failed, because the underlying BIO is not of the correct type
(suitable for file descriptors).

=item E<gt>=0

The file descriptor linked to B<ssl>.

=back

=head1 SEE ALSO

L<SSL_set_fd(3)>, L<ssl(7)> , L<bio(7)>

=head1 COPYRIGHT

Copyright 2000-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
