=pod

=head1 NAME

SSL_get_conn_close_info, SSL_CONN_CLOSE_FLAG_LOCAL,
SSL_CONN_CLOSE_FLAG_TRANSPORT,
OSSL_QUIC_ERR_NO_ERROR,
OSSL_QUIC_ERR_INTERNAL_ERROR,
OSSL_QUIC_ERR_CONNECTION_REFUSED,
OSSL_QUIC_ERR_FLOW_CONTROL_ERROR,
OSSL_QUIC_ERR_STREAM_LIMIT_ERROR,
OSSL_QUIC_ERR_STREAM_STATE_ERROR,
OSSL_QUIC_ERR_FINAL_SIZE_ERROR,
OSSL_QUIC_ERR_FRAME_ENCODING_ERROR,
OSSL_QUIC_ERR_TRANSPORT_PARAMETER_ERROR,
OSSL_QUIC_ERR_CONNECTION_ID_LIMIT_ERROR,
OSSL_QUIC_ERR_PROTOCOL_VIOLATION,
OSSL_QUIC_ERR_INVALID_TOKEN,
OSSL_QUIC_ERR_APPLICATION_ERROR,
OSSL_QUIC_ERR_CRYPTO_BUFFER_EXCEEDED,
OSSL_QUIC_ERR_KEY_UPDATE_ERROR,
OSSL_QUIC_ERR_AEAD_LIMIT_REACHED,
OSSL_QUIC_ERR_NO_VIABLE_PATH,
OSSL_QUIC_ERR_CRYPTO_ERR_BEGIN,
OSSL_QUIC_ERR_CRYPTO_ERR_END,
OSSL_QUIC_ERR_CRYPTO_ERR,
OSSL_QUIC_LOCAL_ERR_IDLE_TIMEOUT
- get information about why a QUIC connection was closed

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 #define SSL_CONN_CLOSE_FLAG_LOCAL
 #define SSL_CONN_CLOSE_FLAG_TRANSPORT

 typedef struct ssl_conn_close_info_st {
     uint64_t error_code, frame_type;
     char     *reason;
     size_t   reason_len;
     uint32_t flags;
 } SSL_CONN_CLOSE_INFO;

 int SSL_get_conn_close_info(SSL *ssl, SSL_CONN_CLOSE_INFO *info,
                             size_t info_len);

 #define OSSL_QUIC_ERR_NO_ERROR                  0x00
 #define OSSL_QUIC_ERR_INTERNAL_ERROR            0x01
 #define OSSL_QUIC_ERR_CONNECTION_REFUSED        0x02
 #define OSSL_QUIC_ERR_FLOW_CONTROL_ERROR        0x03
 #define OSSL_QUIC_ERR_STREAM_LIMIT_ERROR        0x04
 #define OSSL_QUIC_ERR_STREAM_STATE_ERROR        0x05
 #define OSSL_QUIC_ERR_FINAL_SIZE_ERROR          0x06
 #define OSSL_QUIC_ERR_FRAME_ENCODING_ERROR      0x07
 #define OSSL_QUIC_ERR_TRANSPORT_PARAMETER_ERROR 0x08
 #define OSSL_QUIC_ERR_CONNECTION_ID_LIMIT_ERROR 0x09
 #define OSSL_QUIC_ERR_PROTOCOL_VIOLATION        0x0A
 #define OSSL_QUIC_ERR_INVALID_TOKEN             0x0B
 #define OSSL_QUIC_ERR_APPLICATION_ERROR         0x0C
 #define OSSL_QUIC_ERR_CRYPTO_BUFFER_EXCEEDED    0x0D
 #define OSSL_QUIC_ERR_KEY_UPDATE_ERROR          0x0E
 #define OSSL_QUIC_ERR_AEAD_LIMIT_REACHED        0x0F
 #define OSSL_QUIC_ERR_NO_VIABLE_PATH            0x10

 /* Inclusive range for handshake-specific errors. */
 #define OSSL_QUIC_ERR_CRYPTO_ERR_BEGIN          0x0100
 #define OSSL_QUIC_ERR_CRYPTO_ERR_END            0x01FF

 #define OSSL_QUIC_ERR_CRYPTO_ERR(X)

 #define OSSL_QUIC_LOCAL_ERR_IDLE_TIMEOUT

=head1 DESCRIPTION

The SSL_get_conn_close_info() function provides information about why and how a
QUIC connection was closed.

Connection closure information is written to I<*info>, which must be non-NULL.
I<info_len> must be set to C<sizeof(*info)>.

The following fields are set:

=over 4

=item I<error_code>

This is a 62-bit QUIC error code. It is either a 62-bit application error code
(if B<SSL_CONN_CLOSE_FLAG_TRANSPORT> not set in I<flags>) or a  62-bit standard
QUIC transport error code (if B<SSL_CONN_CLOSE_FLAG_TRANSPORT> is set in
I<flags>).

=item I<frame_type>

If B<SSL_CONN_CLOSE_FLAG_TRANSPORT> is set, this may be set to a QUIC frame type
number which caused the connection to be closed. It may also be set to 0 if no
frame type was specified as causing the connection to be closed. If
B<SSL_CONN_CLOSE_FLAG_TRANSPORT> is not set, this is set to 0.

=item I<reason>

If non-NULL, this is intended to be a UTF-8 textual string briefly describing
the reason for connection closure. The length of the reason string in bytes is
given in I<reason_len>. While, if non-NULL, OpenSSL guarantees that this string
will be zero terminated, consider that this buffer may originate from the
(untrusted) peer and thus may also contain zero bytes elsewhere. Therefore, use
of I<reason_len> is recommended.

While it is intended as per the QUIC protocol that this be a UTF-8 string, there
is no guarantee that this is the case for strings received from the peer.

=item B<SSL_CONN_CLOSE_FLAG_LOCAL>

If I<flags> has B<SSL_CONN_CLOSE_FLAG_LOCAL> set, connection closure was locally
triggered. This could be due to an application request (e.g. if
B<SSL_CONN_CLOSE_FLAG_TRANSPORT> is unset), or (if
I<SSL_CONN_CLOSE_FLAG_TRANSPORT> is set) due to logic internal to the QUIC
implementation (for example, if the peer engages in a protocol violation, or an
idle timeout occurs).

If unset, connection closure was remotely triggered.

=item B<SSL_CONN_CLOSE_FLAG_TRANSPORT>

If I<flags> has B<SSL_CONN_CLOSE_FLAG_TRANSPORT> set, connection closure was
triggered for QUIC protocol reasons. Otherwise, connection closure was triggered
by the local or remote application.

=back

The B<OSSL_QUIC_ERR> macro definitions provide the QUIC transport error codes as
defined by RFC 9000. The OSSL_QUIC_ERR_CRYPTO_ERR() macro can be used to convert
a TLS alert code into a QUIC transport error code by mapping it into the range
reserved for such codes by RFC 9000. This range begins at
B<OSSL_QUIC_ERR_CRYPTO_ERR_BEGIN> and ends at B<OSSL_QUIC_ERR_CRYPTO_ERR_END>
inclusive.

=head1 NON-STANDARD TRANSPORT ERROR CODES

Some conditions which can cause QUIC connection termination are not signalled on
the wire and therefore do not have standard error codes. OpenSSL indicates these
errors via SSL_get_conn_close_info() by setting B<SSL_CONN_CLOSE_FLAG_TRANSPORT>
and using one of the following error values. These codes are specific to
OpenSSL, and cannot be sent over the wire, as they are above 2**62.

=over 4

=item B<OSSL_QUIC_LOCAL_ERR_IDLE_TIMEOUT>

The connection was terminated immediately due to the idle timeout expiring.

=back

=head1 RETURN VALUES

SSL_get_conn_close_info() returns 1 on success and 0 on failure. This function
fails if called on a QUIC connection SSL object which has not yet been
terminated. It also fails if called on a QUIC stream SSL object or a non-QUIC
SSL object.

=head1 SEE ALSO

L<SSL_shutdown_ex(3)>

=head1 HISTORY

This function was added in OpenSSL 3.2.

=head1 COPYRIGHT

Copyright 2002-2024 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
