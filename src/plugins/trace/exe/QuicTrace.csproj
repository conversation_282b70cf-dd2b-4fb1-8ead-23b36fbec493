<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.Performance.SDK" Version="1.0.16" />
    <PackageReference Include="Microsoft.Performance.Toolkit.Engine" Version="1.0.16" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\dll\QuicTraceLib.csproj" />
  </ItemGroup>
  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="echo f | xcopy $(TargetDir)*.dll $(SolutionDir)..\..\artifacts\bin\quictrace\$(Configuration)\ /Y&#xD;&#xA;echo f | xcopy $(TargetDir)*.exe $(SolutionDir)..\..\artifacts\bin\quictrace\$(Configuration)\ /Y&#xD;&#xA;echo f | xcopy $(TargetDir)*.pdb $(SolutionDir)..\..\artifacts\bin\quictrace\$(Configuration)\ /Y&#xD;&#xA;echo f | xcopy $(TargetDir)*.json $(SolutionDir)..\..\artifacts\bin\quictrace\$(Configuration)\ /Y" />
  </Target>
</Project>
