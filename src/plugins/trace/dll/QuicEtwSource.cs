﻿//
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
//

using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using Microsoft.Performance.SDK.Processing;

namespace QuicTrace
{
    /// <summary>
    /// This custom data source defines processes MsQuic ETW events.
    /// </summary>
    [ProcessingSource(
        "{FA99CEB6-7043-42FA-97BA-932337EE19F5}",
        "MsQuic ETW Event",
        "ETW events generated by MsQuic")]
    [FileDataSource("etl", "Event Trace Log")]
    public class QuicEtwSource : ProcessingSource
    {
        private IApplicationEnvironment? applicationEnvironment;

        protected override ICustomDataProcessor CreateProcessorCore(
            IEnumerable<IDataSource> dataSources,
            IProcessorEnvironment processorEnvironment,
            ProcessorOptions options)
        {
            Debug.Assert(!(applicationEnvironment is null));

            return new QuicEventProcessor(
                new QuicEventParser(dataSources.Select(x => x.Uri.LocalPath).ToArray(), QuicEventSourceType.ETW),
                options,
                applicationEnvironment,
                processorEnvironment);
        }

        /// <summary>
        /// This method is called to perform additional checks on the data source, to confirm that the data is contains
        /// can be processed. This is helpful for common file extensions, such as ".xml" or ".log". This method could
        /// peek inside at the contents confirm whether it is associated with this custom data source.
        ///
        /// For this sample, we just assume that if the file name is a match, it is handled by this add-in.
        /// </summary>
        /// <param name="dataSource">Path to the source file</param>
        /// <returns>true when <param name="dataSource"> is handled by this add-in</param></returns>
        protected override bool IsDataSourceSupportedCore(IDataSource dataSource)
        {
            return true;
        }

        /// <summary>
        /// This method just saves the application environment so that it can be used later.
        /// </summary>
        /// <param name="applicationEnvironment">Contains information helpful to future processing</param>
        protected override void SetApplicationEnvironmentCore(IApplicationEnvironment applicationEnvironment)
        {
            this.applicationEnvironment = applicationEnvironment;
        }
    }
}
