/* automatically generated by rust-bindgen 0.71.1 */

#[repr(C)]
#[derive(<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>q, <PERSON><PERSON>, Ord, PartialEq, PartialOrd)]
pub struct __BindgenBitfieldUnit<Storage> {
    storage: Storage,
}
impl<Storage> __BindgenBitfieldUnit<Storage> {
    #[inline]
    pub const fn new(storage: Storage) -> Self {
        Self { storage }
    }
}
impl<Storage> __BindgenBitfieldUnit<Storage>
where
    Storage: AsRef<[u8]> + AsMut<[u8]>,
{
    #[inline]
    fn extract_bit(byte: u8, index: usize) -> bool {
        let bit_index = if cfg!(target_endian = "big") {
            7 - (index % 8)
        } else {
            index % 8
        };
        let mask = 1 << bit_index;
        byte & mask == mask
    }
    #[inline]
    pub fn get_bit(&self, index: usize) -> bool {
        debug_assert!(index / 8 < self.storage.as_ref().len());
        let byte_index = index / 8;
        let byte = self.storage.as_ref()[byte_index];
        Self::extract_bit(byte, index)
    }
    #[inline]
    pub unsafe fn raw_get_bit(this: *const Self, index: usize) -> bool {
        debug_assert!(index / 8 < core::mem::size_of::<Storage>());
        let byte_index = index / 8;
        let byte = *(core::ptr::addr_of!((*this).storage) as *const u8).offset(byte_index as isize);
        Self::extract_bit(byte, index)
    }
    #[inline]
    fn change_bit(byte: u8, index: usize, val: bool) -> u8 {
        let bit_index = if cfg!(target_endian = "big") {
            7 - (index % 8)
        } else {
            index % 8
        };
        let mask = 1 << bit_index;
        if val {
            byte | mask
        } else {
            byte & !mask
        }
    }
    #[inline]
    pub fn set_bit(&mut self, index: usize, val: bool) {
        debug_assert!(index / 8 < self.storage.as_ref().len());
        let byte_index = index / 8;
        let byte = &mut self.storage.as_mut()[byte_index];
        *byte = Self::change_bit(*byte, index, val);
    }
    #[inline]
    pub unsafe fn raw_set_bit(this: *mut Self, index: usize, val: bool) {
        debug_assert!(index / 8 < core::mem::size_of::<Storage>());
        let byte_index = index / 8;
        let byte =
            (core::ptr::addr_of_mut!((*this).storage) as *mut u8).offset(byte_index as isize);
        *byte = Self::change_bit(*byte, index, val);
    }
    #[inline]
    pub fn get(&self, bit_offset: usize, bit_width: u8) -> u64 {
        debug_assert!(bit_width <= 64);
        debug_assert!(bit_offset / 8 < self.storage.as_ref().len());
        debug_assert!((bit_offset + (bit_width as usize)) / 8 <= self.storage.as_ref().len());
        let mut val = 0;
        for i in 0..(bit_width as usize) {
            if self.get_bit(i + bit_offset) {
                let index = if cfg!(target_endian = "big") {
                    bit_width as usize - 1 - i
                } else {
                    i
                };
                val |= 1 << index;
            }
        }
        val
    }
    #[inline]
    pub unsafe fn raw_get(this: *const Self, bit_offset: usize, bit_width: u8) -> u64 {
        debug_assert!(bit_width <= 64);
        debug_assert!(bit_offset / 8 < core::mem::size_of::<Storage>());
        debug_assert!((bit_offset + (bit_width as usize)) / 8 <= core::mem::size_of::<Storage>());
        let mut val = 0;
        for i in 0..(bit_width as usize) {
            if Self::raw_get_bit(this, i + bit_offset) {
                let index = if cfg!(target_endian = "big") {
                    bit_width as usize - 1 - i
                } else {
                    i
                };
                val |= 1 << index;
            }
        }
        val
    }
    #[inline]
    pub fn set(&mut self, bit_offset: usize, bit_width: u8, val: u64) {
        debug_assert!(bit_width <= 64);
        debug_assert!(bit_offset / 8 < self.storage.as_ref().len());
        debug_assert!((bit_offset + (bit_width as usize)) / 8 <= self.storage.as_ref().len());
        for i in 0..(bit_width as usize) {
            let mask = 1 << i;
            let val_bit_is_set = val & mask == mask;
            let index = if cfg!(target_endian = "big") {
                bit_width as usize - 1 - i
            } else {
                i
            };
            self.set_bit(index + bit_offset, val_bit_is_set);
        }
    }
    #[inline]
    pub unsafe fn raw_set(this: *mut Self, bit_offset: usize, bit_width: u8, val: u64) {
        debug_assert!(bit_width <= 64);
        debug_assert!(bit_offset / 8 < core::mem::size_of::<Storage>());
        debug_assert!((bit_offset + (bit_width as usize)) / 8 <= core::mem::size_of::<Storage>());
        for i in 0..(bit_width as usize) {
            let mask = 1 << i;
            let val_bit_is_set = val & mask == mask;
            let index = if cfg!(target_endian = "big") {
                bit_width as usize - 1 - i
            } else {
                i
            };
            Self::raw_set_bit(this, index + bit_offset, val_bit_is_set);
        }
    }
}
pub const QUIC_ADDRESS_FAMILY_UNSPEC: u32 = 0;
pub const QUIC_ADDRESS_FAMILY_INET: u32 = 2;
pub const QUIC_ADDRESS_FAMILY_INET6: u32 = 23;
pub const QUIC_UINT62_MAX: u64 = 4611686018427387903;
pub const QUIC_MAX_ALPN_LENGTH: u32 = 255;
pub const QUIC_MAX_SNI_LENGTH: u32 = 65535;
pub const QUIC_MAX_RESUMPTION_APP_DATA_LENGTH: u32 = 1000;
pub const QUIC_STATELESS_RESET_KEY_LENGTH: u32 = 32;
pub const QUIC_MAX_TICKET_KEY_COUNT: u32 = 16;
pub const QUIC_TLS_SECRETS_MAX_SECRET_LEN: u32 = 64;
pub const QUIC_PARAM_PREFIX_GLOBAL: u32 = 16777216;
pub const QUIC_PARAM_PREFIX_REGISTRATION: u32 = 33554432;
pub const QUIC_PARAM_PREFIX_CONFIGURATION: u32 = 50331648;
pub const QUIC_PARAM_PREFIX_LISTENER: u32 = 67108864;
pub const QUIC_PARAM_PREFIX_CONNECTION: u32 = 83886080;
pub const QUIC_PARAM_PREFIX_TLS: u32 = 100663296;
pub const QUIC_PARAM_PREFIX_TLS_SCHANNEL: u32 = 117440512;
pub const QUIC_PARAM_PREFIX_STREAM: u32 = 134217728;
pub const QUIC_PARAM_HIGH_PRIORITY: u32 = 1073741824;
pub const QUIC_PARAM_GLOBAL_RETRY_MEMORY_PERCENT: u32 = 16777216;
pub const QUIC_PARAM_GLOBAL_SUPPORTED_VERSIONS: u32 = 16777217;
pub const QUIC_PARAM_GLOBAL_LOAD_BALACING_MODE: u32 = 16777218;
pub const QUIC_PARAM_GLOBAL_PERF_COUNTERS: u32 = 16777219;
pub const QUIC_PARAM_GLOBAL_LIBRARY_VERSION: u32 = 16777220;
pub const QUIC_PARAM_GLOBAL_SETTINGS: u32 = 16777221;
pub const QUIC_PARAM_GLOBAL_GLOBAL_SETTINGS: u32 = 16777222;
pub const QUIC_PARAM_GLOBAL_VERSION_SETTINGS: u32 = 16777223;
pub const QUIC_PARAM_GLOBAL_LIBRARY_GIT_HASH: u32 = 16777224;
pub const QUIC_PARAM_GLOBAL_EXECUTION_CONFIG: u32 = 16777225;
pub const QUIC_PARAM_GLOBAL_TLS_PROVIDER: u32 = 16777226;
pub const QUIC_PARAM_GLOBAL_STATELESS_RESET_KEY: u32 = 16777227;
pub const QUIC_PARAM_GLOBAL_STATISTICS_V2_SIZES: u32 = 16777228;
pub const QUIC_PARAM_CONFIGURATION_SETTINGS: u32 = 50331648;
pub const QUIC_PARAM_CONFIGURATION_TICKET_KEYS: u32 = 50331649;
pub const QUIC_PARAM_CONFIGURATION_VERSION_SETTINGS: u32 = 50331650;
pub const QUIC_PARAM_CONFIGURATION_SCHANNEL_CREDENTIAL_ATTRIBUTE_W: u32 = 50331651;
pub const QUIC_PARAM_LISTENER_LOCAL_ADDRESS: u32 = 67108864;
pub const QUIC_PARAM_LISTENER_STATS: u32 = 67108865;
pub const QUIC_PARAM_LISTENER_CIBIR_ID: u32 = 67108866;
pub const QUIC_PARAM_DOS_MODE_EVENTS: u32 = 67108868;
pub const QUIC_PARAM_CONN_QUIC_VERSION: u32 = 83886080;
pub const QUIC_PARAM_CONN_LOCAL_ADDRESS: u32 = 83886081;
pub const QUIC_PARAM_CONN_REMOTE_ADDRESS: u32 = 83886082;
pub const QUIC_PARAM_CONN_IDEAL_PROCESSOR: u32 = 83886083;
pub const QUIC_PARAM_CONN_SETTINGS: u32 = 83886084;
pub const QUIC_PARAM_CONN_STATISTICS: u32 = 83886085;
pub const QUIC_PARAM_CONN_STATISTICS_PLAT: u32 = 83886086;
pub const QUIC_PARAM_CONN_SHARE_UDP_BINDING: u32 = 83886087;
pub const QUIC_PARAM_CONN_LOCAL_BIDI_STREAM_COUNT: u32 = 83886088;
pub const QUIC_PARAM_CONN_LOCAL_UNIDI_STREAM_COUNT: u32 = 83886089;
pub const QUIC_PARAM_CONN_MAX_STREAM_IDS: u32 = 83886090;
pub const QUIC_PARAM_CONN_CLOSE_REASON_PHRASE: u32 = 83886091;
pub const QUIC_PARAM_CONN_STREAM_SCHEDULING_SCHEME: u32 = 83886092;
pub const QUIC_PARAM_CONN_DATAGRAM_RECEIVE_ENABLED: u32 = 83886093;
pub const QUIC_PARAM_CONN_DATAGRAM_SEND_ENABLED: u32 = 83886094;
pub const QUIC_PARAM_CONN_RESUMPTION_TICKET: u32 = 83886096;
pub const QUIC_PARAM_CONN_PEER_CERTIFICATE_VALID: u32 = 83886097;
pub const QUIC_PARAM_CONN_LOCAL_INTERFACE: u32 = 83886098;
pub const QUIC_PARAM_CONN_TLS_SECRETS: u32 = 83886099;
pub const QUIC_PARAM_CONN_VERSION_SETTINGS: u32 = 83886100;
pub const QUIC_PARAM_CONN_CIBIR_ID: u32 = 83886101;
pub const QUIC_PARAM_CONN_STATISTICS_V2: u32 = 83886102;
pub const QUIC_PARAM_CONN_STATISTICS_V2_PLAT: u32 = 83886103;
pub const QUIC_PARAM_CONN_ORIG_DEST_CID: u32 = 83886104;
pub const QUIC_PARAM_CONN_SEND_DSCP: u32 = 83886105;
pub const QUIC_PARAM_CONN_NETWORK_STATISTICS: u32 = 83886112;
pub const QUIC_PARAM_TLS_HANDSHAKE_INFO: u32 = 100663296;
pub const QUIC_PARAM_TLS_NEGOTIATED_ALPN: u32 = 100663297;
pub const QUIC_PARAM_TLS_SCHANNEL_CONTEXT_ATTRIBUTE_W: u32 = 117440512;
pub const QUIC_PARAM_TLS_SCHANNEL_CONTEXT_ATTRIBUTE_EX_W: u32 = 117440513;
pub const QUIC_PARAM_TLS_SCHANNEL_SECURITY_CONTEXT_TOKEN: u32 = 117440514;
pub const QUIC_PARAM_STREAM_ID: u32 = 134217728;
pub const QUIC_PARAM_STREAM_0RTT_LENGTH: u32 = 134217729;
pub const QUIC_PARAM_STREAM_IDEAL_SEND_BUFFER_SIZE: u32 = 134217730;
pub const QUIC_PARAM_STREAM_PRIORITY: u32 = 134217731;
pub const QUIC_PARAM_STREAM_STATISTICS: u32 = 134217732;
pub const QUIC_PARAM_STREAM_RELIABLE_OFFSET: u32 = 134217733;
pub const QUIC_API_VERSION_1: u32 = 1;
pub const QUIC_API_VERSION_2: u32 = 2;
pub type BYTE = ::std::os::raw::c_uchar;
pub type HRESULT = ::std::os::raw::c_long;
pub type BOOLEAN = BYTE;
pub type QUIC_ADDRESS_FAMILY = ADDRESS_FAMILY;
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_ADDR_STR {
    pub Address: [::std::os::raw::c_char; 64usize],
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_ADDR_STR"][::std::mem::size_of::<QUIC_ADDR_STR>() - 64usize];
    ["Alignment of QUIC_ADDR_STR"][::std::mem::align_of::<QUIC_ADDR_STR>() - 1usize];
    ["Offset of field: QUIC_ADDR_STR::Address"]
        [::std::mem::offset_of!(QUIC_ADDR_STR, Address) - 0usize];
};
pub type QUIC_EVENTQ = HANDLE;
pub type QUIC_CQE = OVERLAPPED_ENTRY;
pub type QUIC_EVENT_COMPLETION = ::std::option::Option<unsafe extern "C" fn(Cqe: *mut QUIC_CQE)>;
pub type QUIC_EVENT_COMPLETION_HANDLER = QUIC_EVENT_COMPLETION;
#[repr(C)]
pub struct QUIC_SQE {
    pub Overlapped: OVERLAPPED,
    pub Completion: QUIC_EVENT_COMPLETION_HANDLER,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_SQE"][::std::mem::size_of::<QUIC_SQE>() - 40usize];
    ["Alignment of QUIC_SQE"][::std::mem::align_of::<QUIC_SQE>() - 8usize];
    ["Offset of field: QUIC_SQE::Overlapped"]
        [::std::mem::offset_of!(QUIC_SQE, Overlapped) - 0usize];
    ["Offset of field: QUIC_SQE::Completion"]
        [::std::mem::offset_of!(QUIC_SQE, Completion) - 32usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_HANDLE {
    _unused: [u8; 0],
}
pub type HQUIC = *mut QUIC_HANDLE;
pub type QUIC_UINT62 = u64;
pub const QUIC_TLS_PROVIDER_QUIC_TLS_PROVIDER_SCHANNEL: QUIC_TLS_PROVIDER = 0;
pub const QUIC_TLS_PROVIDER_QUIC_TLS_PROVIDER_OPENSSL: QUIC_TLS_PROVIDER = 1;
pub type QUIC_TLS_PROVIDER = ::std::os::raw::c_int;
pub const QUIC_EXECUTION_PROFILE_QUIC_EXECUTION_PROFILE_LOW_LATENCY: QUIC_EXECUTION_PROFILE = 0;
pub const QUIC_EXECUTION_PROFILE_QUIC_EXECUTION_PROFILE_TYPE_MAX_THROUGHPUT:
    QUIC_EXECUTION_PROFILE = 1;
pub const QUIC_EXECUTION_PROFILE_QUIC_EXECUTION_PROFILE_TYPE_SCAVENGER: QUIC_EXECUTION_PROFILE = 2;
pub const QUIC_EXECUTION_PROFILE_QUIC_EXECUTION_PROFILE_TYPE_REAL_TIME: QUIC_EXECUTION_PROFILE = 3;
pub type QUIC_EXECUTION_PROFILE = ::std::os::raw::c_int;
pub const QUIC_LOAD_BALANCING_MODE_QUIC_LOAD_BALANCING_DISABLED: QUIC_LOAD_BALANCING_MODE = 0;
pub const QUIC_LOAD_BALANCING_MODE_QUIC_LOAD_BALANCING_SERVER_ID_IP: QUIC_LOAD_BALANCING_MODE = 1;
pub const QUIC_LOAD_BALANCING_MODE_QUIC_LOAD_BALANCING_SERVER_ID_FIXED: QUIC_LOAD_BALANCING_MODE =
    2;
pub const QUIC_LOAD_BALANCING_MODE_QUIC_LOAD_BALANCING_COUNT: QUIC_LOAD_BALANCING_MODE = 3;
pub type QUIC_LOAD_BALANCING_MODE = ::std::os::raw::c_int;
pub const QUIC_TLS_ALERT_CODES_QUIC_TLS_ALERT_CODE_SUCCESS: QUIC_TLS_ALERT_CODES = 65535;
pub const QUIC_TLS_ALERT_CODES_QUIC_TLS_ALERT_CODE_UNEXPECTED_MESSAGE: QUIC_TLS_ALERT_CODES = 10;
pub const QUIC_TLS_ALERT_CODES_QUIC_TLS_ALERT_CODE_BAD_CERTIFICATE: QUIC_TLS_ALERT_CODES = 42;
pub const QUIC_TLS_ALERT_CODES_QUIC_TLS_ALERT_CODE_UNSUPPORTED_CERTIFICATE: QUIC_TLS_ALERT_CODES =
    43;
pub const QUIC_TLS_ALERT_CODES_QUIC_TLS_ALERT_CODE_CERTIFICATE_REVOKED: QUIC_TLS_ALERT_CODES = 44;
pub const QUIC_TLS_ALERT_CODES_QUIC_TLS_ALERT_CODE_CERTIFICATE_EXPIRED: QUIC_TLS_ALERT_CODES = 45;
pub const QUIC_TLS_ALERT_CODES_QUIC_TLS_ALERT_CODE_CERTIFICATE_UNKNOWN: QUIC_TLS_ALERT_CODES = 46;
pub const QUIC_TLS_ALERT_CODES_QUIC_TLS_ALERT_CODE_ILLEGAL_PARAMETER: QUIC_TLS_ALERT_CODES = 47;
pub const QUIC_TLS_ALERT_CODES_QUIC_TLS_ALERT_CODE_UNKNOWN_CA: QUIC_TLS_ALERT_CODES = 48;
pub const QUIC_TLS_ALERT_CODES_QUIC_TLS_ALERT_CODE_ACCESS_DENIED: QUIC_TLS_ALERT_CODES = 49;
pub const QUIC_TLS_ALERT_CODES_QUIC_TLS_ALERT_CODE_INSUFFICIENT_SECURITY: QUIC_TLS_ALERT_CODES = 71;
pub const QUIC_TLS_ALERT_CODES_QUIC_TLS_ALERT_CODE_INTERNAL_ERROR: QUIC_TLS_ALERT_CODES = 80;
pub const QUIC_TLS_ALERT_CODES_QUIC_TLS_ALERT_CODE_USER_CANCELED: QUIC_TLS_ALERT_CODES = 90;
pub const QUIC_TLS_ALERT_CODES_QUIC_TLS_ALERT_CODE_CERTIFICATE_REQUIRED: QUIC_TLS_ALERT_CODES = 116;
pub const QUIC_TLS_ALERT_CODES_QUIC_TLS_ALERT_CODE_MAX: QUIC_TLS_ALERT_CODES = 255;
pub type QUIC_TLS_ALERT_CODES = ::std::os::raw::c_int;
pub const QUIC_CREDENTIAL_TYPE_QUIC_CREDENTIAL_TYPE_NONE: QUIC_CREDENTIAL_TYPE = 0;
pub const QUIC_CREDENTIAL_TYPE_QUIC_CREDENTIAL_TYPE_CERTIFICATE_HASH: QUIC_CREDENTIAL_TYPE = 1;
pub const QUIC_CREDENTIAL_TYPE_QUIC_CREDENTIAL_TYPE_CERTIFICATE_HASH_STORE: QUIC_CREDENTIAL_TYPE =
    2;
pub const QUIC_CREDENTIAL_TYPE_QUIC_CREDENTIAL_TYPE_CERTIFICATE_CONTEXT: QUIC_CREDENTIAL_TYPE = 3;
pub const QUIC_CREDENTIAL_TYPE_QUIC_CREDENTIAL_TYPE_CERTIFICATE_FILE: QUIC_CREDENTIAL_TYPE = 4;
pub const QUIC_CREDENTIAL_TYPE_QUIC_CREDENTIAL_TYPE_CERTIFICATE_FILE_PROTECTED:
    QUIC_CREDENTIAL_TYPE = 5;
pub const QUIC_CREDENTIAL_TYPE_QUIC_CREDENTIAL_TYPE_CERTIFICATE_PKCS12: QUIC_CREDENTIAL_TYPE = 6;
pub type QUIC_CREDENTIAL_TYPE = ::std::os::raw::c_int;
pub const QUIC_CREDENTIAL_FLAGS_QUIC_CREDENTIAL_FLAG_NONE: QUIC_CREDENTIAL_FLAGS = 0;
pub const QUIC_CREDENTIAL_FLAGS_QUIC_CREDENTIAL_FLAG_CLIENT: QUIC_CREDENTIAL_FLAGS = 1;
pub const QUIC_CREDENTIAL_FLAGS_QUIC_CREDENTIAL_FLAG_LOAD_ASYNCHRONOUS: QUIC_CREDENTIAL_FLAGS = 2;
pub const QUIC_CREDENTIAL_FLAGS_QUIC_CREDENTIAL_FLAG_NO_CERTIFICATE_VALIDATION:
    QUIC_CREDENTIAL_FLAGS = 4;
pub const QUIC_CREDENTIAL_FLAGS_QUIC_CREDENTIAL_FLAG_ENABLE_OCSP: QUIC_CREDENTIAL_FLAGS = 8;
pub const QUIC_CREDENTIAL_FLAGS_QUIC_CREDENTIAL_FLAG_INDICATE_CERTIFICATE_RECEIVED:
    QUIC_CREDENTIAL_FLAGS = 16;
pub const QUIC_CREDENTIAL_FLAGS_QUIC_CREDENTIAL_FLAG_DEFER_CERTIFICATE_VALIDATION:
    QUIC_CREDENTIAL_FLAGS = 32;
pub const QUIC_CREDENTIAL_FLAGS_QUIC_CREDENTIAL_FLAG_REQUIRE_CLIENT_AUTHENTICATION:
    QUIC_CREDENTIAL_FLAGS = 64;
pub const QUIC_CREDENTIAL_FLAGS_QUIC_CREDENTIAL_FLAG_USE_TLS_BUILTIN_CERTIFICATE_VALIDATION:
    QUIC_CREDENTIAL_FLAGS = 128;
pub const QUIC_CREDENTIAL_FLAGS_QUIC_CREDENTIAL_FLAG_REVOCATION_CHECK_END_CERT:
    QUIC_CREDENTIAL_FLAGS = 256;
pub const QUIC_CREDENTIAL_FLAGS_QUIC_CREDENTIAL_FLAG_REVOCATION_CHECK_CHAIN: QUIC_CREDENTIAL_FLAGS =
    512;
pub const QUIC_CREDENTIAL_FLAGS_QUIC_CREDENTIAL_FLAG_REVOCATION_CHECK_CHAIN_EXCLUDE_ROOT:
    QUIC_CREDENTIAL_FLAGS = 1024;
pub const QUIC_CREDENTIAL_FLAGS_QUIC_CREDENTIAL_FLAG_IGNORE_NO_REVOCATION_CHECK:
    QUIC_CREDENTIAL_FLAGS = 2048;
pub const QUIC_CREDENTIAL_FLAGS_QUIC_CREDENTIAL_FLAG_IGNORE_REVOCATION_OFFLINE:
    QUIC_CREDENTIAL_FLAGS = 4096;
pub const QUIC_CREDENTIAL_FLAGS_QUIC_CREDENTIAL_FLAG_SET_ALLOWED_CIPHER_SUITES:
    QUIC_CREDENTIAL_FLAGS = 8192;
pub const QUIC_CREDENTIAL_FLAGS_QUIC_CREDENTIAL_FLAG_USE_PORTABLE_CERTIFICATES:
    QUIC_CREDENTIAL_FLAGS = 16384;
pub const QUIC_CREDENTIAL_FLAGS_QUIC_CREDENTIAL_FLAG_USE_SUPPLIED_CREDENTIALS:
    QUIC_CREDENTIAL_FLAGS = 32768;
pub const QUIC_CREDENTIAL_FLAGS_QUIC_CREDENTIAL_FLAG_USE_SYSTEM_MAPPER: QUIC_CREDENTIAL_FLAGS =
    65536;
pub const QUIC_CREDENTIAL_FLAGS_QUIC_CREDENTIAL_FLAG_CACHE_ONLY_URL_RETRIEVAL:
    QUIC_CREDENTIAL_FLAGS = 131072;
pub const QUIC_CREDENTIAL_FLAGS_QUIC_CREDENTIAL_FLAG_REVOCATION_CHECK_CACHE_ONLY:
    QUIC_CREDENTIAL_FLAGS = 262144;
pub const QUIC_CREDENTIAL_FLAGS_QUIC_CREDENTIAL_FLAG_INPROC_PEER_CERTIFICATE:
    QUIC_CREDENTIAL_FLAGS = 524288;
pub const QUIC_CREDENTIAL_FLAGS_QUIC_CREDENTIAL_FLAG_SET_CA_CERTIFICATE_FILE:
    QUIC_CREDENTIAL_FLAGS = 1048576;
pub const QUIC_CREDENTIAL_FLAGS_QUIC_CREDENTIAL_FLAG_DISABLE_AIA: QUIC_CREDENTIAL_FLAGS = 2097152;
pub type QUIC_CREDENTIAL_FLAGS = ::std::os::raw::c_int;
pub const QUIC_ALLOWED_CIPHER_SUITE_FLAGS_QUIC_ALLOWED_CIPHER_SUITE_NONE:
    QUIC_ALLOWED_CIPHER_SUITE_FLAGS = 0;
pub const QUIC_ALLOWED_CIPHER_SUITE_FLAGS_QUIC_ALLOWED_CIPHER_SUITE_AES_128_GCM_SHA256:
    QUIC_ALLOWED_CIPHER_SUITE_FLAGS = 1;
pub const QUIC_ALLOWED_CIPHER_SUITE_FLAGS_QUIC_ALLOWED_CIPHER_SUITE_AES_256_GCM_SHA384:
    QUIC_ALLOWED_CIPHER_SUITE_FLAGS = 2;
pub const QUIC_ALLOWED_CIPHER_SUITE_FLAGS_QUIC_ALLOWED_CIPHER_SUITE_CHACHA20_POLY1305_SHA256:
    QUIC_ALLOWED_CIPHER_SUITE_FLAGS = 4;
pub type QUIC_ALLOWED_CIPHER_SUITE_FLAGS = ::std::os::raw::c_int;
pub const QUIC_CERTIFICATE_HASH_STORE_FLAGS_QUIC_CERTIFICATE_HASH_STORE_FLAG_NONE:
    QUIC_CERTIFICATE_HASH_STORE_FLAGS = 0;
pub const QUIC_CERTIFICATE_HASH_STORE_FLAGS_QUIC_CERTIFICATE_HASH_STORE_FLAG_MACHINE_STORE:
    QUIC_CERTIFICATE_HASH_STORE_FLAGS = 1;
pub type QUIC_CERTIFICATE_HASH_STORE_FLAGS = ::std::os::raw::c_int;
pub const QUIC_CONNECTION_SHUTDOWN_FLAGS_QUIC_CONNECTION_SHUTDOWN_FLAG_NONE:
    QUIC_CONNECTION_SHUTDOWN_FLAGS = 0;
pub const QUIC_CONNECTION_SHUTDOWN_FLAGS_QUIC_CONNECTION_SHUTDOWN_FLAG_SILENT:
    QUIC_CONNECTION_SHUTDOWN_FLAGS = 1;
pub type QUIC_CONNECTION_SHUTDOWN_FLAGS = ::std::os::raw::c_int;
pub const QUIC_SERVER_RESUMPTION_LEVEL_QUIC_SERVER_NO_RESUME: QUIC_SERVER_RESUMPTION_LEVEL = 0;
pub const QUIC_SERVER_RESUMPTION_LEVEL_QUIC_SERVER_RESUME_ONLY: QUIC_SERVER_RESUMPTION_LEVEL = 1;
pub const QUIC_SERVER_RESUMPTION_LEVEL_QUIC_SERVER_RESUME_AND_ZERORTT:
    QUIC_SERVER_RESUMPTION_LEVEL = 2;
pub type QUIC_SERVER_RESUMPTION_LEVEL = ::std::os::raw::c_int;
pub const QUIC_SEND_RESUMPTION_FLAGS_QUIC_SEND_RESUMPTION_FLAG_NONE: QUIC_SEND_RESUMPTION_FLAGS = 0;
pub const QUIC_SEND_RESUMPTION_FLAGS_QUIC_SEND_RESUMPTION_FLAG_FINAL: QUIC_SEND_RESUMPTION_FLAGS =
    1;
pub type QUIC_SEND_RESUMPTION_FLAGS = ::std::os::raw::c_int;
pub const QUIC_STREAM_SCHEDULING_SCHEME_QUIC_STREAM_SCHEDULING_SCHEME_FIFO:
    QUIC_STREAM_SCHEDULING_SCHEME = 0;
pub const QUIC_STREAM_SCHEDULING_SCHEME_QUIC_STREAM_SCHEDULING_SCHEME_ROUND_ROBIN:
    QUIC_STREAM_SCHEDULING_SCHEME = 1;
pub const QUIC_STREAM_SCHEDULING_SCHEME_QUIC_STREAM_SCHEDULING_SCHEME_COUNT:
    QUIC_STREAM_SCHEDULING_SCHEME = 2;
pub type QUIC_STREAM_SCHEDULING_SCHEME = ::std::os::raw::c_int;
pub const QUIC_STREAM_OPEN_FLAGS_QUIC_STREAM_OPEN_FLAG_NONE: QUIC_STREAM_OPEN_FLAGS = 0;
pub const QUIC_STREAM_OPEN_FLAGS_QUIC_STREAM_OPEN_FLAG_UNIDIRECTIONAL: QUIC_STREAM_OPEN_FLAGS = 1;
pub const QUIC_STREAM_OPEN_FLAGS_QUIC_STREAM_OPEN_FLAG_0_RTT: QUIC_STREAM_OPEN_FLAGS = 2;
pub const QUIC_STREAM_OPEN_FLAGS_QUIC_STREAM_OPEN_FLAG_DELAY_ID_FC_UPDATES: QUIC_STREAM_OPEN_FLAGS =
    4;
pub const QUIC_STREAM_OPEN_FLAGS_QUIC_STREAM_OPEN_FLAG_APP_OWNED_BUFFERS: QUIC_STREAM_OPEN_FLAGS =
    8;
pub type QUIC_STREAM_OPEN_FLAGS = ::std::os::raw::c_int;
pub const QUIC_STREAM_START_FLAGS_QUIC_STREAM_START_FLAG_NONE: QUIC_STREAM_START_FLAGS = 0;
pub const QUIC_STREAM_START_FLAGS_QUIC_STREAM_START_FLAG_IMMEDIATE: QUIC_STREAM_START_FLAGS = 1;
pub const QUIC_STREAM_START_FLAGS_QUIC_STREAM_START_FLAG_FAIL_BLOCKED: QUIC_STREAM_START_FLAGS = 2;
pub const QUIC_STREAM_START_FLAGS_QUIC_STREAM_START_FLAG_SHUTDOWN_ON_FAIL: QUIC_STREAM_START_FLAGS =
    4;
pub const QUIC_STREAM_START_FLAGS_QUIC_STREAM_START_FLAG_INDICATE_PEER_ACCEPT:
    QUIC_STREAM_START_FLAGS = 8;
pub const QUIC_STREAM_START_FLAGS_QUIC_STREAM_START_FLAG_PRIORITY_WORK: QUIC_STREAM_START_FLAGS =
    16;
pub type QUIC_STREAM_START_FLAGS = ::std::os::raw::c_int;
pub const QUIC_STREAM_SHUTDOWN_FLAGS_QUIC_STREAM_SHUTDOWN_FLAG_NONE: QUIC_STREAM_SHUTDOWN_FLAGS = 0;
pub const QUIC_STREAM_SHUTDOWN_FLAGS_QUIC_STREAM_SHUTDOWN_FLAG_GRACEFUL:
    QUIC_STREAM_SHUTDOWN_FLAGS = 1;
pub const QUIC_STREAM_SHUTDOWN_FLAGS_QUIC_STREAM_SHUTDOWN_FLAG_ABORT_SEND:
    QUIC_STREAM_SHUTDOWN_FLAGS = 2;
pub const QUIC_STREAM_SHUTDOWN_FLAGS_QUIC_STREAM_SHUTDOWN_FLAG_ABORT_RECEIVE:
    QUIC_STREAM_SHUTDOWN_FLAGS = 4;
pub const QUIC_STREAM_SHUTDOWN_FLAGS_QUIC_STREAM_SHUTDOWN_FLAG_ABORT: QUIC_STREAM_SHUTDOWN_FLAGS =
    6;
pub const QUIC_STREAM_SHUTDOWN_FLAGS_QUIC_STREAM_SHUTDOWN_FLAG_IMMEDIATE:
    QUIC_STREAM_SHUTDOWN_FLAGS = 8;
pub const QUIC_STREAM_SHUTDOWN_FLAGS_QUIC_STREAM_SHUTDOWN_FLAG_INLINE: QUIC_STREAM_SHUTDOWN_FLAGS =
    16;
pub type QUIC_STREAM_SHUTDOWN_FLAGS = ::std::os::raw::c_int;
pub const QUIC_RECEIVE_FLAGS_QUIC_RECEIVE_FLAG_NONE: QUIC_RECEIVE_FLAGS = 0;
pub const QUIC_RECEIVE_FLAGS_QUIC_RECEIVE_FLAG_0_RTT: QUIC_RECEIVE_FLAGS = 1;
pub const QUIC_RECEIVE_FLAGS_QUIC_RECEIVE_FLAG_FIN: QUIC_RECEIVE_FLAGS = 2;
pub type QUIC_RECEIVE_FLAGS = ::std::os::raw::c_int;
pub const QUIC_SEND_FLAGS_QUIC_SEND_FLAG_NONE: QUIC_SEND_FLAGS = 0;
pub const QUIC_SEND_FLAGS_QUIC_SEND_FLAG_ALLOW_0_RTT: QUIC_SEND_FLAGS = 1;
pub const QUIC_SEND_FLAGS_QUIC_SEND_FLAG_START: QUIC_SEND_FLAGS = 2;
pub const QUIC_SEND_FLAGS_QUIC_SEND_FLAG_FIN: QUIC_SEND_FLAGS = 4;
pub const QUIC_SEND_FLAGS_QUIC_SEND_FLAG_DGRAM_PRIORITY: QUIC_SEND_FLAGS = 8;
pub const QUIC_SEND_FLAGS_QUIC_SEND_FLAG_DELAY_SEND: QUIC_SEND_FLAGS = 16;
pub const QUIC_SEND_FLAGS_QUIC_SEND_FLAG_CANCEL_ON_LOSS: QUIC_SEND_FLAGS = 32;
pub const QUIC_SEND_FLAGS_QUIC_SEND_FLAG_PRIORITY_WORK: QUIC_SEND_FLAGS = 64;
pub const QUIC_SEND_FLAGS_QUIC_SEND_FLAG_CANCEL_ON_BLOCKED: QUIC_SEND_FLAGS = 128;
pub type QUIC_SEND_FLAGS = ::std::os::raw::c_int;
pub const QUIC_DATAGRAM_SEND_STATE_QUIC_DATAGRAM_SEND_UNKNOWN: QUIC_DATAGRAM_SEND_STATE = 0;
pub const QUIC_DATAGRAM_SEND_STATE_QUIC_DATAGRAM_SEND_SENT: QUIC_DATAGRAM_SEND_STATE = 1;
pub const QUIC_DATAGRAM_SEND_STATE_QUIC_DATAGRAM_SEND_LOST_SUSPECT: QUIC_DATAGRAM_SEND_STATE = 2;
pub const QUIC_DATAGRAM_SEND_STATE_QUIC_DATAGRAM_SEND_LOST_DISCARDED: QUIC_DATAGRAM_SEND_STATE = 3;
pub const QUIC_DATAGRAM_SEND_STATE_QUIC_DATAGRAM_SEND_ACKNOWLEDGED: QUIC_DATAGRAM_SEND_STATE = 4;
pub const QUIC_DATAGRAM_SEND_STATE_QUIC_DATAGRAM_SEND_ACKNOWLEDGED_SPURIOUS:
    QUIC_DATAGRAM_SEND_STATE = 5;
pub const QUIC_DATAGRAM_SEND_STATE_QUIC_DATAGRAM_SEND_CANCELED: QUIC_DATAGRAM_SEND_STATE = 6;
pub type QUIC_DATAGRAM_SEND_STATE = ::std::os::raw::c_int;
pub const QUIC_GLOBAL_EXECUTION_CONFIG_FLAGS_QUIC_GLOBAL_EXECUTION_CONFIG_FLAG_NONE:
    QUIC_GLOBAL_EXECUTION_CONFIG_FLAGS = 0;
pub const QUIC_GLOBAL_EXECUTION_CONFIG_FLAGS_QUIC_GLOBAL_EXECUTION_CONFIG_FLAG_NO_IDEAL_PROC:
    QUIC_GLOBAL_EXECUTION_CONFIG_FLAGS = 8;
pub const QUIC_GLOBAL_EXECUTION_CONFIG_FLAGS_QUIC_GLOBAL_EXECUTION_CONFIG_FLAG_HIGH_PRIORITY:
    QUIC_GLOBAL_EXECUTION_CONFIG_FLAGS = 16;
pub const QUIC_GLOBAL_EXECUTION_CONFIG_FLAGS_QUIC_GLOBAL_EXECUTION_CONFIG_FLAG_AFFINITIZE:
    QUIC_GLOBAL_EXECUTION_CONFIG_FLAGS = 32;
pub type QUIC_GLOBAL_EXECUTION_CONFIG_FLAGS = ::std::os::raw::c_int;
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_GLOBAL_EXECUTION_CONFIG {
    pub Flags: QUIC_GLOBAL_EXECUTION_CONFIG_FLAGS,
    pub PollingIdleTimeoutUs: u32,
    pub ProcessorCount: u32,
    pub ProcessorList: [u16; 1usize],
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_GLOBAL_EXECUTION_CONFIG"]
        [::std::mem::size_of::<QUIC_GLOBAL_EXECUTION_CONFIG>() - 16usize];
    ["Alignment of QUIC_GLOBAL_EXECUTION_CONFIG"]
        [::std::mem::align_of::<QUIC_GLOBAL_EXECUTION_CONFIG>() - 4usize];
    ["Offset of field: QUIC_GLOBAL_EXECUTION_CONFIG::Flags"]
        [::std::mem::offset_of!(QUIC_GLOBAL_EXECUTION_CONFIG, Flags) - 0usize];
    ["Offset of field: QUIC_GLOBAL_EXECUTION_CONFIG::PollingIdleTimeoutUs"]
        [::std::mem::offset_of!(QUIC_GLOBAL_EXECUTION_CONFIG, PollingIdleTimeoutUs) - 4usize];
    ["Offset of field: QUIC_GLOBAL_EXECUTION_CONFIG::ProcessorCount"]
        [::std::mem::offset_of!(QUIC_GLOBAL_EXECUTION_CONFIG, ProcessorCount) - 8usize];
    ["Offset of field: QUIC_GLOBAL_EXECUTION_CONFIG::ProcessorList"]
        [::std::mem::offset_of!(QUIC_GLOBAL_EXECUTION_CONFIG, ProcessorList) - 12usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_EXECUTION_CONFIG {
    pub IdealProcessor: u32,
    pub EventQ: *mut QUIC_EVENTQ,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_EXECUTION_CONFIG"][::std::mem::size_of::<QUIC_EXECUTION_CONFIG>() - 16usize];
    ["Alignment of QUIC_EXECUTION_CONFIG"]
        [::std::mem::align_of::<QUIC_EXECUTION_CONFIG>() - 8usize];
    ["Offset of field: QUIC_EXECUTION_CONFIG::IdealProcessor"]
        [::std::mem::offset_of!(QUIC_EXECUTION_CONFIG, IdealProcessor) - 0usize];
    ["Offset of field: QUIC_EXECUTION_CONFIG::EventQ"]
        [::std::mem::offset_of!(QUIC_EXECUTION_CONFIG, EventQ) - 8usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_EXECUTION {
    _unused: [u8; 0],
}
pub type QUIC_EXECUTION_CREATE_FN = ::std::option::Option<
    unsafe extern "C" fn(
        Flags: QUIC_GLOBAL_EXECUTION_CONFIG_FLAGS,
        PollingIdleTimeoutUs: u32,
        Count: u32,
        Configs: *mut QUIC_EXECUTION_CONFIG,
        Executions: *mut *mut QUIC_EXECUTION,
    ) -> HRESULT,
>;
pub type QUIC_EXECUTION_DELETE_FN =
    ::std::option::Option<unsafe extern "C" fn(Count: u32, Executions: *mut *mut QUIC_EXECUTION)>;
pub type QUIC_EXECUTION_POLL_FN =
    ::std::option::Option<unsafe extern "C" fn(Execution: *mut QUIC_EXECUTION) -> u32>;
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_REGISTRATION_CONFIG {
    pub AppName: *const ::std::os::raw::c_char,
    pub ExecutionProfile: QUIC_EXECUTION_PROFILE,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_REGISTRATION_CONFIG"]
        [::std::mem::size_of::<QUIC_REGISTRATION_CONFIG>() - 16usize];
    ["Alignment of QUIC_REGISTRATION_CONFIG"]
        [::std::mem::align_of::<QUIC_REGISTRATION_CONFIG>() - 8usize];
    ["Offset of field: QUIC_REGISTRATION_CONFIG::AppName"]
        [::std::mem::offset_of!(QUIC_REGISTRATION_CONFIG, AppName) - 0usize];
    ["Offset of field: QUIC_REGISTRATION_CONFIG::ExecutionProfile"]
        [::std::mem::offset_of!(QUIC_REGISTRATION_CONFIG, ExecutionProfile) - 8usize];
};
pub type QUIC_CREDENTIAL_LOAD_COMPLETE_HANDLER = ::std::option::Option<
    unsafe extern "C" fn(arg1: HQUIC, arg2: *mut ::std::os::raw::c_void, arg3: HRESULT),
>;
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_CERTIFICATE_HASH {
    pub ShaHash: [u8; 20usize],
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_CERTIFICATE_HASH"][::std::mem::size_of::<QUIC_CERTIFICATE_HASH>() - 20usize];
    ["Alignment of QUIC_CERTIFICATE_HASH"]
        [::std::mem::align_of::<QUIC_CERTIFICATE_HASH>() - 1usize];
    ["Offset of field: QUIC_CERTIFICATE_HASH::ShaHash"]
        [::std::mem::offset_of!(QUIC_CERTIFICATE_HASH, ShaHash) - 0usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_CERTIFICATE_HASH_STORE {
    pub Flags: QUIC_CERTIFICATE_HASH_STORE_FLAGS,
    pub ShaHash: [u8; 20usize],
    pub StoreName: [::std::os::raw::c_char; 128usize],
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_CERTIFICATE_HASH_STORE"]
        [::std::mem::size_of::<QUIC_CERTIFICATE_HASH_STORE>() - 152usize];
    ["Alignment of QUIC_CERTIFICATE_HASH_STORE"]
        [::std::mem::align_of::<QUIC_CERTIFICATE_HASH_STORE>() - 4usize];
    ["Offset of field: QUIC_CERTIFICATE_HASH_STORE::Flags"]
        [::std::mem::offset_of!(QUIC_CERTIFICATE_HASH_STORE, Flags) - 0usize];
    ["Offset of field: QUIC_CERTIFICATE_HASH_STORE::ShaHash"]
        [::std::mem::offset_of!(QUIC_CERTIFICATE_HASH_STORE, ShaHash) - 4usize];
    ["Offset of field: QUIC_CERTIFICATE_HASH_STORE::StoreName"]
        [::std::mem::offset_of!(QUIC_CERTIFICATE_HASH_STORE, StoreName) - 24usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_CERTIFICATE_FILE {
    pub PrivateKeyFile: *const ::std::os::raw::c_char,
    pub CertificateFile: *const ::std::os::raw::c_char,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_CERTIFICATE_FILE"][::std::mem::size_of::<QUIC_CERTIFICATE_FILE>() - 16usize];
    ["Alignment of QUIC_CERTIFICATE_FILE"]
        [::std::mem::align_of::<QUIC_CERTIFICATE_FILE>() - 8usize];
    ["Offset of field: QUIC_CERTIFICATE_FILE::PrivateKeyFile"]
        [::std::mem::offset_of!(QUIC_CERTIFICATE_FILE, PrivateKeyFile) - 0usize];
    ["Offset of field: QUIC_CERTIFICATE_FILE::CertificateFile"]
        [::std::mem::offset_of!(QUIC_CERTIFICATE_FILE, CertificateFile) - 8usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_CERTIFICATE_FILE_PROTECTED {
    pub PrivateKeyFile: *const ::std::os::raw::c_char,
    pub CertificateFile: *const ::std::os::raw::c_char,
    pub PrivateKeyPassword: *const ::std::os::raw::c_char,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_CERTIFICATE_FILE_PROTECTED"]
        [::std::mem::size_of::<QUIC_CERTIFICATE_FILE_PROTECTED>() - 24usize];
    ["Alignment of QUIC_CERTIFICATE_FILE_PROTECTED"]
        [::std::mem::align_of::<QUIC_CERTIFICATE_FILE_PROTECTED>() - 8usize];
    ["Offset of field: QUIC_CERTIFICATE_FILE_PROTECTED::PrivateKeyFile"]
        [::std::mem::offset_of!(QUIC_CERTIFICATE_FILE_PROTECTED, PrivateKeyFile) - 0usize];
    ["Offset of field: QUIC_CERTIFICATE_FILE_PROTECTED::CertificateFile"]
        [::std::mem::offset_of!(QUIC_CERTIFICATE_FILE_PROTECTED, CertificateFile) - 8usize];
    ["Offset of field: QUIC_CERTIFICATE_FILE_PROTECTED::PrivateKeyPassword"]
        [::std::mem::offset_of!(QUIC_CERTIFICATE_FILE_PROTECTED, PrivateKeyPassword) - 16usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_CERTIFICATE_PKCS12 {
    pub Asn1Blob: *const u8,
    pub Asn1BlobLength: u32,
    pub PrivateKeyPassword: *const ::std::os::raw::c_char,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_CERTIFICATE_PKCS12"][::std::mem::size_of::<QUIC_CERTIFICATE_PKCS12>() - 24usize];
    ["Alignment of QUIC_CERTIFICATE_PKCS12"]
        [::std::mem::align_of::<QUIC_CERTIFICATE_PKCS12>() - 8usize];
    ["Offset of field: QUIC_CERTIFICATE_PKCS12::Asn1Blob"]
        [::std::mem::offset_of!(QUIC_CERTIFICATE_PKCS12, Asn1Blob) - 0usize];
    ["Offset of field: QUIC_CERTIFICATE_PKCS12::Asn1BlobLength"]
        [::std::mem::offset_of!(QUIC_CERTIFICATE_PKCS12, Asn1BlobLength) - 8usize];
    ["Offset of field: QUIC_CERTIFICATE_PKCS12::PrivateKeyPassword"]
        [::std::mem::offset_of!(QUIC_CERTIFICATE_PKCS12, PrivateKeyPassword) - 16usize];
};
pub type QUIC_CERTIFICATE = ::std::os::raw::c_void;
pub type QUIC_CERTIFICATE_CHAIN = ::std::os::raw::c_void;
#[repr(C)]
#[derive(Copy, Clone)]
pub struct QUIC_CREDENTIAL_CONFIG {
    pub Type: QUIC_CREDENTIAL_TYPE,
    pub Flags: QUIC_CREDENTIAL_FLAGS,
    pub __bindgen_anon_1: QUIC_CREDENTIAL_CONFIG__bindgen_ty_1,
    pub Principal: *const ::std::os::raw::c_char,
    pub Reserved: *mut ::std::os::raw::c_void,
    pub AsyncHandler: QUIC_CREDENTIAL_LOAD_COMPLETE_HANDLER,
    pub AllowedCipherSuites: QUIC_ALLOWED_CIPHER_SUITE_FLAGS,
    pub CaCertificateFile: *const ::std::os::raw::c_char,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union QUIC_CREDENTIAL_CONFIG__bindgen_ty_1 {
    pub CertificateHash: *mut QUIC_CERTIFICATE_HASH,
    pub CertificateHashStore: *mut QUIC_CERTIFICATE_HASH_STORE,
    pub CertificateContext: *mut QUIC_CERTIFICATE,
    pub CertificateFile: *mut QUIC_CERTIFICATE_FILE,
    pub CertificateFileProtected: *mut QUIC_CERTIFICATE_FILE_PROTECTED,
    pub CertificatePkcs12: *mut QUIC_CERTIFICATE_PKCS12,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_CREDENTIAL_CONFIG__bindgen_ty_1"]
        [::std::mem::size_of::<QUIC_CREDENTIAL_CONFIG__bindgen_ty_1>() - 8usize];
    ["Alignment of QUIC_CREDENTIAL_CONFIG__bindgen_ty_1"]
        [::std::mem::align_of::<QUIC_CREDENTIAL_CONFIG__bindgen_ty_1>() - 8usize];
    ["Offset of field: QUIC_CREDENTIAL_CONFIG__bindgen_ty_1::CertificateHash"]
        [::std::mem::offset_of!(QUIC_CREDENTIAL_CONFIG__bindgen_ty_1, CertificateHash) - 0usize];
    ["Offset of field: QUIC_CREDENTIAL_CONFIG__bindgen_ty_1::CertificateHashStore"][::std::mem::offset_of!(
        QUIC_CREDENTIAL_CONFIG__bindgen_ty_1,
        CertificateHashStore
    ) - 0usize];
    ["Offset of field: QUIC_CREDENTIAL_CONFIG__bindgen_ty_1::CertificateContext"]
        [::std::mem::offset_of!(QUIC_CREDENTIAL_CONFIG__bindgen_ty_1, CertificateContext) - 0usize];
    ["Offset of field: QUIC_CREDENTIAL_CONFIG__bindgen_ty_1::CertificateFile"]
        [::std::mem::offset_of!(QUIC_CREDENTIAL_CONFIG__bindgen_ty_1, CertificateFile) - 0usize];
    ["Offset of field: QUIC_CREDENTIAL_CONFIG__bindgen_ty_1::CertificateFileProtected"][::std::mem::offset_of!(
        QUIC_CREDENTIAL_CONFIG__bindgen_ty_1,
        CertificateFileProtected
    ) - 0usize];
    ["Offset of field: QUIC_CREDENTIAL_CONFIG__bindgen_ty_1::CertificatePkcs12"]
        [::std::mem::offset_of!(QUIC_CREDENTIAL_CONFIG__bindgen_ty_1, CertificatePkcs12) - 0usize];
};
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_CREDENTIAL_CONFIG"][::std::mem::size_of::<QUIC_CREDENTIAL_CONFIG>() - 56usize];
    ["Alignment of QUIC_CREDENTIAL_CONFIG"]
        [::std::mem::align_of::<QUIC_CREDENTIAL_CONFIG>() - 8usize];
    ["Offset of field: QUIC_CREDENTIAL_CONFIG::Type"]
        [::std::mem::offset_of!(QUIC_CREDENTIAL_CONFIG, Type) - 0usize];
    ["Offset of field: QUIC_CREDENTIAL_CONFIG::Flags"]
        [::std::mem::offset_of!(QUIC_CREDENTIAL_CONFIG, Flags) - 4usize];
    ["Offset of field: QUIC_CREDENTIAL_CONFIG::Principal"]
        [::std::mem::offset_of!(QUIC_CREDENTIAL_CONFIG, Principal) - 16usize];
    ["Offset of field: QUIC_CREDENTIAL_CONFIG::Reserved"]
        [::std::mem::offset_of!(QUIC_CREDENTIAL_CONFIG, Reserved) - 24usize];
    ["Offset of field: QUIC_CREDENTIAL_CONFIG::AsyncHandler"]
        [::std::mem::offset_of!(QUIC_CREDENTIAL_CONFIG, AsyncHandler) - 32usize];
    ["Offset of field: QUIC_CREDENTIAL_CONFIG::AllowedCipherSuites"]
        [::std::mem::offset_of!(QUIC_CREDENTIAL_CONFIG, AllowedCipherSuites) - 40usize];
    ["Offset of field: QUIC_CREDENTIAL_CONFIG::CaCertificateFile"]
        [::std::mem::offset_of!(QUIC_CREDENTIAL_CONFIG, CaCertificateFile) - 48usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_TICKET_KEY_CONFIG {
    pub Id: [u8; 16usize],
    pub Material: [u8; 64usize],
    pub MaterialLength: u8,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_TICKET_KEY_CONFIG"][::std::mem::size_of::<QUIC_TICKET_KEY_CONFIG>() - 81usize];
    ["Alignment of QUIC_TICKET_KEY_CONFIG"]
        [::std::mem::align_of::<QUIC_TICKET_KEY_CONFIG>() - 1usize];
    ["Offset of field: QUIC_TICKET_KEY_CONFIG::Id"]
        [::std::mem::offset_of!(QUIC_TICKET_KEY_CONFIG, Id) - 0usize];
    ["Offset of field: QUIC_TICKET_KEY_CONFIG::Material"]
        [::std::mem::offset_of!(QUIC_TICKET_KEY_CONFIG, Material) - 16usize];
    ["Offset of field: QUIC_TICKET_KEY_CONFIG::MaterialLength"]
        [::std::mem::offset_of!(QUIC_TICKET_KEY_CONFIG, MaterialLength) - 80usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_BUFFER {
    pub Length: u32,
    pub Buffer: *mut u8,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_BUFFER"][::std::mem::size_of::<QUIC_BUFFER>() - 16usize];
    ["Alignment of QUIC_BUFFER"][::std::mem::align_of::<QUIC_BUFFER>() - 8usize];
    ["Offset of field: QUIC_BUFFER::Length"][::std::mem::offset_of!(QUIC_BUFFER, Length) - 0usize];
    ["Offset of field: QUIC_BUFFER::Buffer"][::std::mem::offset_of!(QUIC_BUFFER, Buffer) - 8usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_NEW_CONNECTION_INFO {
    pub QuicVersion: u32,
    pub LocalAddress: *const QUIC_ADDR,
    pub RemoteAddress: *const QUIC_ADDR,
    pub CryptoBufferLength: u32,
    pub ClientAlpnListLength: u16,
    pub ServerNameLength: u16,
    pub NegotiatedAlpnLength: u8,
    pub CryptoBuffer: *const u8,
    pub ClientAlpnList: *const u8,
    pub NegotiatedAlpn: *const u8,
    pub ServerName: *const ::std::os::raw::c_char,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_NEW_CONNECTION_INFO"]
        [::std::mem::size_of::<QUIC_NEW_CONNECTION_INFO>() - 72usize];
    ["Alignment of QUIC_NEW_CONNECTION_INFO"]
        [::std::mem::align_of::<QUIC_NEW_CONNECTION_INFO>() - 8usize];
    ["Offset of field: QUIC_NEW_CONNECTION_INFO::QuicVersion"]
        [::std::mem::offset_of!(QUIC_NEW_CONNECTION_INFO, QuicVersion) - 0usize];
    ["Offset of field: QUIC_NEW_CONNECTION_INFO::LocalAddress"]
        [::std::mem::offset_of!(QUIC_NEW_CONNECTION_INFO, LocalAddress) - 8usize];
    ["Offset of field: QUIC_NEW_CONNECTION_INFO::RemoteAddress"]
        [::std::mem::offset_of!(QUIC_NEW_CONNECTION_INFO, RemoteAddress) - 16usize];
    ["Offset of field: QUIC_NEW_CONNECTION_INFO::CryptoBufferLength"]
        [::std::mem::offset_of!(QUIC_NEW_CONNECTION_INFO, CryptoBufferLength) - 24usize];
    ["Offset of field: QUIC_NEW_CONNECTION_INFO::ClientAlpnListLength"]
        [::std::mem::offset_of!(QUIC_NEW_CONNECTION_INFO, ClientAlpnListLength) - 28usize];
    ["Offset of field: QUIC_NEW_CONNECTION_INFO::ServerNameLength"]
        [::std::mem::offset_of!(QUIC_NEW_CONNECTION_INFO, ServerNameLength) - 30usize];
    ["Offset of field: QUIC_NEW_CONNECTION_INFO::NegotiatedAlpnLength"]
        [::std::mem::offset_of!(QUIC_NEW_CONNECTION_INFO, NegotiatedAlpnLength) - 32usize];
    ["Offset of field: QUIC_NEW_CONNECTION_INFO::CryptoBuffer"]
        [::std::mem::offset_of!(QUIC_NEW_CONNECTION_INFO, CryptoBuffer) - 40usize];
    ["Offset of field: QUIC_NEW_CONNECTION_INFO::ClientAlpnList"]
        [::std::mem::offset_of!(QUIC_NEW_CONNECTION_INFO, ClientAlpnList) - 48usize];
    ["Offset of field: QUIC_NEW_CONNECTION_INFO::NegotiatedAlpn"]
        [::std::mem::offset_of!(QUIC_NEW_CONNECTION_INFO, NegotiatedAlpn) - 56usize];
    ["Offset of field: QUIC_NEW_CONNECTION_INFO::ServerName"]
        [::std::mem::offset_of!(QUIC_NEW_CONNECTION_INFO, ServerName) - 64usize];
};
pub const QUIC_TLS_PROTOCOL_VERSION_QUIC_TLS_PROTOCOL_UNKNOWN: QUIC_TLS_PROTOCOL_VERSION = 0;
pub const QUIC_TLS_PROTOCOL_VERSION_QUIC_TLS_PROTOCOL_1_3: QUIC_TLS_PROTOCOL_VERSION = 12288;
pub type QUIC_TLS_PROTOCOL_VERSION = ::std::os::raw::c_int;
pub const QUIC_CIPHER_ALGORITHM_QUIC_CIPHER_ALGORITHM_NONE: QUIC_CIPHER_ALGORITHM = 0;
pub const QUIC_CIPHER_ALGORITHM_QUIC_CIPHER_ALGORITHM_AES_128: QUIC_CIPHER_ALGORITHM = 26126;
pub const QUIC_CIPHER_ALGORITHM_QUIC_CIPHER_ALGORITHM_AES_256: QUIC_CIPHER_ALGORITHM = 26128;
pub const QUIC_CIPHER_ALGORITHM_QUIC_CIPHER_ALGORITHM_CHACHA20: QUIC_CIPHER_ALGORITHM = 26130;
pub type QUIC_CIPHER_ALGORITHM = ::std::os::raw::c_int;
pub const QUIC_HASH_ALGORITHM_QUIC_HASH_ALGORITHM_NONE: QUIC_HASH_ALGORITHM = 0;
pub const QUIC_HASH_ALGORITHM_QUIC_HASH_ALGORITHM_SHA_256: QUIC_HASH_ALGORITHM = 32780;
pub const QUIC_HASH_ALGORITHM_QUIC_HASH_ALGORITHM_SHA_384: QUIC_HASH_ALGORITHM = 32781;
pub type QUIC_HASH_ALGORITHM = ::std::os::raw::c_int;
pub const QUIC_KEY_EXCHANGE_ALGORITHM_QUIC_KEY_EXCHANGE_ALGORITHM_NONE:
    QUIC_KEY_EXCHANGE_ALGORITHM = 0;
pub type QUIC_KEY_EXCHANGE_ALGORITHM = ::std::os::raw::c_int;
pub const QUIC_TLS_GROUP_QUIC_TLS_GROUP_UNKNOWN: QUIC_TLS_GROUP = 0;
pub const QUIC_TLS_GROUP_QUIC_TLS_GROUP_SECP256R1: QUIC_TLS_GROUP = 23;
pub const QUIC_TLS_GROUP_QUIC_TLS_GROUP_SECP384R1: QUIC_TLS_GROUP = 24;
pub const QUIC_TLS_GROUP_QUIC_TLS_GROUP_X25519: QUIC_TLS_GROUP = 29;
pub const QUIC_TLS_GROUP_QUIC_TLS_GROUP_MLKEM512: QUIC_TLS_GROUP = 512;
pub const QUIC_TLS_GROUP_QUIC_TLS_GROUP_MLKEM768: QUIC_TLS_GROUP = 513;
pub const QUIC_TLS_GROUP_QUIC_TLS_GROUP_MLKEM1024: QUIC_TLS_GROUP = 514;
pub const QUIC_TLS_GROUP_QUIC_TLS_GROUP_SECP256R1MLKEM768: QUIC_TLS_GROUP = 4587;
pub const QUIC_TLS_GROUP_QUIC_TLS_GROUP_X25519MLKEM768: QUIC_TLS_GROUP = 4588;
pub const QUIC_TLS_GROUP_QUIC_TLS_GROUP_SECP384R1MLKEM1024: QUIC_TLS_GROUP = 4589;
pub type QUIC_TLS_GROUP = ::std::os::raw::c_int;
pub const QUIC_CIPHER_SUITE_QUIC_CIPHER_SUITE_TLS_AES_128_GCM_SHA256: QUIC_CIPHER_SUITE = 4865;
pub const QUIC_CIPHER_SUITE_QUIC_CIPHER_SUITE_TLS_AES_256_GCM_SHA384: QUIC_CIPHER_SUITE = 4866;
pub const QUIC_CIPHER_SUITE_QUIC_CIPHER_SUITE_TLS_CHACHA20_POLY1305_SHA256: QUIC_CIPHER_SUITE =
    4867;
pub type QUIC_CIPHER_SUITE = ::std::os::raw::c_int;
pub const QUIC_CONGESTION_CONTROL_ALGORITHM_QUIC_CONGESTION_CONTROL_ALGORITHM_CUBIC:
    QUIC_CONGESTION_CONTROL_ALGORITHM = 0;
pub const QUIC_CONGESTION_CONTROL_ALGORITHM_QUIC_CONGESTION_CONTROL_ALGORITHM_BBR:
    QUIC_CONGESTION_CONTROL_ALGORITHM = 1;
pub const QUIC_CONGESTION_CONTROL_ALGORITHM_QUIC_CONGESTION_CONTROL_ALGORITHM_MAX:
    QUIC_CONGESTION_CONTROL_ALGORITHM = 2;
pub type QUIC_CONGESTION_CONTROL_ALGORITHM = ::std::os::raw::c_int;
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_HANDSHAKE_INFO {
    pub TlsProtocolVersion: QUIC_TLS_PROTOCOL_VERSION,
    pub CipherAlgorithm: QUIC_CIPHER_ALGORITHM,
    pub CipherStrength: i32,
    pub Hash: QUIC_HASH_ALGORITHM,
    pub HashStrength: i32,
    pub KeyExchangeAlgorithm: QUIC_KEY_EXCHANGE_ALGORITHM,
    pub KeyExchangeStrength: i32,
    pub CipherSuite: QUIC_CIPHER_SUITE,
    pub TlsGroup: QUIC_TLS_GROUP,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_HANDSHAKE_INFO"][::std::mem::size_of::<QUIC_HANDSHAKE_INFO>() - 36usize];
    ["Alignment of QUIC_HANDSHAKE_INFO"][::std::mem::align_of::<QUIC_HANDSHAKE_INFO>() - 4usize];
    ["Offset of field: QUIC_HANDSHAKE_INFO::TlsProtocolVersion"]
        [::std::mem::offset_of!(QUIC_HANDSHAKE_INFO, TlsProtocolVersion) - 0usize];
    ["Offset of field: QUIC_HANDSHAKE_INFO::CipherAlgorithm"]
        [::std::mem::offset_of!(QUIC_HANDSHAKE_INFO, CipherAlgorithm) - 4usize];
    ["Offset of field: QUIC_HANDSHAKE_INFO::CipherStrength"]
        [::std::mem::offset_of!(QUIC_HANDSHAKE_INFO, CipherStrength) - 8usize];
    ["Offset of field: QUIC_HANDSHAKE_INFO::Hash"]
        [::std::mem::offset_of!(QUIC_HANDSHAKE_INFO, Hash) - 12usize];
    ["Offset of field: QUIC_HANDSHAKE_INFO::HashStrength"]
        [::std::mem::offset_of!(QUIC_HANDSHAKE_INFO, HashStrength) - 16usize];
    ["Offset of field: QUIC_HANDSHAKE_INFO::KeyExchangeAlgorithm"]
        [::std::mem::offset_of!(QUIC_HANDSHAKE_INFO, KeyExchangeAlgorithm) - 20usize];
    ["Offset of field: QUIC_HANDSHAKE_INFO::KeyExchangeStrength"]
        [::std::mem::offset_of!(QUIC_HANDSHAKE_INFO, KeyExchangeStrength) - 24usize];
    ["Offset of field: QUIC_HANDSHAKE_INFO::CipherSuite"]
        [::std::mem::offset_of!(QUIC_HANDSHAKE_INFO, CipherSuite) - 28usize];
    ["Offset of field: QUIC_HANDSHAKE_INFO::TlsGroup"]
        [::std::mem::offset_of!(QUIC_HANDSHAKE_INFO, TlsGroup) - 32usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_STATISTICS {
    pub CorrelationId: u64,
    pub _bitfield_align_1: [u8; 0],
    pub _bitfield_1: __BindgenBitfieldUnit<[u8; 1usize]>,
    pub Rtt: u32,
    pub MinRtt: u32,
    pub MaxRtt: u32,
    pub Timing: QUIC_STATISTICS__bindgen_ty_1,
    pub Handshake: QUIC_STATISTICS__bindgen_ty_2,
    pub Send: QUIC_STATISTICS__bindgen_ty_3,
    pub Recv: QUIC_STATISTICS__bindgen_ty_4,
    pub Misc: QUIC_STATISTICS__bindgen_ty_5,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_STATISTICS__bindgen_ty_1 {
    pub Start: u64,
    pub InitialFlightEnd: u64,
    pub HandshakeFlightEnd: u64,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_STATISTICS__bindgen_ty_1"]
        [::std::mem::size_of::<QUIC_STATISTICS__bindgen_ty_1>() - 24usize];
    ["Alignment of QUIC_STATISTICS__bindgen_ty_1"]
        [::std::mem::align_of::<QUIC_STATISTICS__bindgen_ty_1>() - 8usize];
    ["Offset of field: QUIC_STATISTICS__bindgen_ty_1::Start"]
        [::std::mem::offset_of!(QUIC_STATISTICS__bindgen_ty_1, Start) - 0usize];
    ["Offset of field: QUIC_STATISTICS__bindgen_ty_1::InitialFlightEnd"]
        [::std::mem::offset_of!(QUIC_STATISTICS__bindgen_ty_1, InitialFlightEnd) - 8usize];
    ["Offset of field: QUIC_STATISTICS__bindgen_ty_1::HandshakeFlightEnd"]
        [::std::mem::offset_of!(QUIC_STATISTICS__bindgen_ty_1, HandshakeFlightEnd) - 16usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_STATISTICS__bindgen_ty_2 {
    pub ClientFlight1Bytes: u32,
    pub ServerFlight1Bytes: u32,
    pub ClientFlight2Bytes: u32,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_STATISTICS__bindgen_ty_2"]
        [::std::mem::size_of::<QUIC_STATISTICS__bindgen_ty_2>() - 12usize];
    ["Alignment of QUIC_STATISTICS__bindgen_ty_2"]
        [::std::mem::align_of::<QUIC_STATISTICS__bindgen_ty_2>() - 4usize];
    ["Offset of field: QUIC_STATISTICS__bindgen_ty_2::ClientFlight1Bytes"]
        [::std::mem::offset_of!(QUIC_STATISTICS__bindgen_ty_2, ClientFlight1Bytes) - 0usize];
    ["Offset of field: QUIC_STATISTICS__bindgen_ty_2::ServerFlight1Bytes"]
        [::std::mem::offset_of!(QUIC_STATISTICS__bindgen_ty_2, ServerFlight1Bytes) - 4usize];
    ["Offset of field: QUIC_STATISTICS__bindgen_ty_2::ClientFlight2Bytes"]
        [::std::mem::offset_of!(QUIC_STATISTICS__bindgen_ty_2, ClientFlight2Bytes) - 8usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_STATISTICS__bindgen_ty_3 {
    pub PathMtu: u16,
    pub TotalPackets: u64,
    pub RetransmittablePackets: u64,
    pub SuspectedLostPackets: u64,
    pub SpuriousLostPackets: u64,
    pub TotalBytes: u64,
    pub TotalStreamBytes: u64,
    pub CongestionCount: u32,
    pub PersistentCongestionCount: u32,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_STATISTICS__bindgen_ty_3"]
        [::std::mem::size_of::<QUIC_STATISTICS__bindgen_ty_3>() - 64usize];
    ["Alignment of QUIC_STATISTICS__bindgen_ty_3"]
        [::std::mem::align_of::<QUIC_STATISTICS__bindgen_ty_3>() - 8usize];
    ["Offset of field: QUIC_STATISTICS__bindgen_ty_3::PathMtu"]
        [::std::mem::offset_of!(QUIC_STATISTICS__bindgen_ty_3, PathMtu) - 0usize];
    ["Offset of field: QUIC_STATISTICS__bindgen_ty_3::TotalPackets"]
        [::std::mem::offset_of!(QUIC_STATISTICS__bindgen_ty_3, TotalPackets) - 8usize];
    ["Offset of field: QUIC_STATISTICS__bindgen_ty_3::RetransmittablePackets"]
        [::std::mem::offset_of!(QUIC_STATISTICS__bindgen_ty_3, RetransmittablePackets) - 16usize];
    ["Offset of field: QUIC_STATISTICS__bindgen_ty_3::SuspectedLostPackets"]
        [::std::mem::offset_of!(QUIC_STATISTICS__bindgen_ty_3, SuspectedLostPackets) - 24usize];
    ["Offset of field: QUIC_STATISTICS__bindgen_ty_3::SpuriousLostPackets"]
        [::std::mem::offset_of!(QUIC_STATISTICS__bindgen_ty_3, SpuriousLostPackets) - 32usize];
    ["Offset of field: QUIC_STATISTICS__bindgen_ty_3::TotalBytes"]
        [::std::mem::offset_of!(QUIC_STATISTICS__bindgen_ty_3, TotalBytes) - 40usize];
    ["Offset of field: QUIC_STATISTICS__bindgen_ty_3::TotalStreamBytes"]
        [::std::mem::offset_of!(QUIC_STATISTICS__bindgen_ty_3, TotalStreamBytes) - 48usize];
    ["Offset of field: QUIC_STATISTICS__bindgen_ty_3::CongestionCount"]
        [::std::mem::offset_of!(QUIC_STATISTICS__bindgen_ty_3, CongestionCount) - 56usize];
    ["Offset of field: QUIC_STATISTICS__bindgen_ty_3::PersistentCongestionCount"][::std::mem::offset_of!(
        QUIC_STATISTICS__bindgen_ty_3,
        PersistentCongestionCount
    ) - 60usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_STATISTICS__bindgen_ty_4 {
    pub TotalPackets: u64,
    pub ReorderedPackets: u64,
    pub DroppedPackets: u64,
    pub DuplicatePackets: u64,
    pub TotalBytes: u64,
    pub TotalStreamBytes: u64,
    pub DecryptionFailures: u64,
    pub ValidAckFrames: u64,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_STATISTICS__bindgen_ty_4"]
        [::std::mem::size_of::<QUIC_STATISTICS__bindgen_ty_4>() - 64usize];
    ["Alignment of QUIC_STATISTICS__bindgen_ty_4"]
        [::std::mem::align_of::<QUIC_STATISTICS__bindgen_ty_4>() - 8usize];
    ["Offset of field: QUIC_STATISTICS__bindgen_ty_4::TotalPackets"]
        [::std::mem::offset_of!(QUIC_STATISTICS__bindgen_ty_4, TotalPackets) - 0usize];
    ["Offset of field: QUIC_STATISTICS__bindgen_ty_4::ReorderedPackets"]
        [::std::mem::offset_of!(QUIC_STATISTICS__bindgen_ty_4, ReorderedPackets) - 8usize];
    ["Offset of field: QUIC_STATISTICS__bindgen_ty_4::DroppedPackets"]
        [::std::mem::offset_of!(QUIC_STATISTICS__bindgen_ty_4, DroppedPackets) - 16usize];
    ["Offset of field: QUIC_STATISTICS__bindgen_ty_4::DuplicatePackets"]
        [::std::mem::offset_of!(QUIC_STATISTICS__bindgen_ty_4, DuplicatePackets) - 24usize];
    ["Offset of field: QUIC_STATISTICS__bindgen_ty_4::TotalBytes"]
        [::std::mem::offset_of!(QUIC_STATISTICS__bindgen_ty_4, TotalBytes) - 32usize];
    ["Offset of field: QUIC_STATISTICS__bindgen_ty_4::TotalStreamBytes"]
        [::std::mem::offset_of!(QUIC_STATISTICS__bindgen_ty_4, TotalStreamBytes) - 40usize];
    ["Offset of field: QUIC_STATISTICS__bindgen_ty_4::DecryptionFailures"]
        [::std::mem::offset_of!(QUIC_STATISTICS__bindgen_ty_4, DecryptionFailures) - 48usize];
    ["Offset of field: QUIC_STATISTICS__bindgen_ty_4::ValidAckFrames"]
        [::std::mem::offset_of!(QUIC_STATISTICS__bindgen_ty_4, ValidAckFrames) - 56usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_STATISTICS__bindgen_ty_5 {
    pub KeyUpdateCount: u32,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_STATISTICS__bindgen_ty_5"]
        [::std::mem::size_of::<QUIC_STATISTICS__bindgen_ty_5>() - 4usize];
    ["Alignment of QUIC_STATISTICS__bindgen_ty_5"]
        [::std::mem::align_of::<QUIC_STATISTICS__bindgen_ty_5>() - 4usize];
    ["Offset of field: QUIC_STATISTICS__bindgen_ty_5::KeyUpdateCount"]
        [::std::mem::offset_of!(QUIC_STATISTICS__bindgen_ty_5, KeyUpdateCount) - 0usize];
};
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_STATISTICS"][::std::mem::size_of::<QUIC_STATISTICS>() - 200usize];
    ["Alignment of QUIC_STATISTICS"][::std::mem::align_of::<QUIC_STATISTICS>() - 8usize];
    ["Offset of field: QUIC_STATISTICS::CorrelationId"]
        [::std::mem::offset_of!(QUIC_STATISTICS, CorrelationId) - 0usize];
    ["Offset of field: QUIC_STATISTICS::Rtt"]
        [::std::mem::offset_of!(QUIC_STATISTICS, Rtt) - 12usize];
    ["Offset of field: QUIC_STATISTICS::MinRtt"]
        [::std::mem::offset_of!(QUIC_STATISTICS, MinRtt) - 16usize];
    ["Offset of field: QUIC_STATISTICS::MaxRtt"]
        [::std::mem::offset_of!(QUIC_STATISTICS, MaxRtt) - 20usize];
    ["Offset of field: QUIC_STATISTICS::Timing"]
        [::std::mem::offset_of!(QUIC_STATISTICS, Timing) - 24usize];
    ["Offset of field: QUIC_STATISTICS::Handshake"]
        [::std::mem::offset_of!(QUIC_STATISTICS, Handshake) - 48usize];
    ["Offset of field: QUIC_STATISTICS::Send"]
        [::std::mem::offset_of!(QUIC_STATISTICS, Send) - 64usize];
    ["Offset of field: QUIC_STATISTICS::Recv"]
        [::std::mem::offset_of!(QUIC_STATISTICS, Recv) - 128usize];
    ["Offset of field: QUIC_STATISTICS::Misc"]
        [::std::mem::offset_of!(QUIC_STATISTICS, Misc) - 192usize];
};
impl QUIC_STATISTICS {
    #[inline]
    pub fn VersionNegotiation(&self) -> u32 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(0usize, 1u8) as u32) }
    }
    #[inline]
    pub fn set_VersionNegotiation(&mut self, val: u32) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            self._bitfield_1.set(0usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn VersionNegotiation_raw(this: *const Self) -> u32 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                0usize,
                1u8,
            ) as u32)
        }
    }
    #[inline]
    pub unsafe fn set_VersionNegotiation_raw(this: *mut Self, val: u32) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                0usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn StatelessRetry(&self) -> u32 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(1usize, 1u8) as u32) }
    }
    #[inline]
    pub fn set_StatelessRetry(&mut self, val: u32) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            self._bitfield_1.set(1usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn StatelessRetry_raw(this: *const Self) -> u32 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                1usize,
                1u8,
            ) as u32)
        }
    }
    #[inline]
    pub unsafe fn set_StatelessRetry_raw(this: *mut Self, val: u32) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                1usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn ResumptionAttempted(&self) -> u32 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(2usize, 1u8) as u32) }
    }
    #[inline]
    pub fn set_ResumptionAttempted(&mut self, val: u32) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            self._bitfield_1.set(2usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn ResumptionAttempted_raw(this: *const Self) -> u32 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                2usize,
                1u8,
            ) as u32)
        }
    }
    #[inline]
    pub unsafe fn set_ResumptionAttempted_raw(this: *mut Self, val: u32) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                2usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn ResumptionSucceeded(&self) -> u32 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(3usize, 1u8) as u32) }
    }
    #[inline]
    pub fn set_ResumptionSucceeded(&mut self, val: u32) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            self._bitfield_1.set(3usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn ResumptionSucceeded_raw(this: *const Self) -> u32 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                3usize,
                1u8,
            ) as u32)
        }
    }
    #[inline]
    pub unsafe fn set_ResumptionSucceeded_raw(this: *mut Self, val: u32) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                3usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn new_bitfield_1(
        VersionNegotiation: u32,
        StatelessRetry: u32,
        ResumptionAttempted: u32,
        ResumptionSucceeded: u32,
    ) -> __BindgenBitfieldUnit<[u8; 1usize]> {
        let mut __bindgen_bitfield_unit: __BindgenBitfieldUnit<[u8; 1usize]> = Default::default();
        __bindgen_bitfield_unit.set(0usize, 1u8, {
            let VersionNegotiation: u32 = unsafe { ::std::mem::transmute(VersionNegotiation) };
            VersionNegotiation as u64
        });
        __bindgen_bitfield_unit.set(1usize, 1u8, {
            let StatelessRetry: u32 = unsafe { ::std::mem::transmute(StatelessRetry) };
            StatelessRetry as u64
        });
        __bindgen_bitfield_unit.set(2usize, 1u8, {
            let ResumptionAttempted: u32 = unsafe { ::std::mem::transmute(ResumptionAttempted) };
            ResumptionAttempted as u64
        });
        __bindgen_bitfield_unit.set(3usize, 1u8, {
            let ResumptionSucceeded: u32 = unsafe { ::std::mem::transmute(ResumptionSucceeded) };
            ResumptionSucceeded as u64
        });
        __bindgen_bitfield_unit
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_STATISTICS_V2 {
    pub CorrelationId: u64,
    pub _bitfield_align_1: [u32; 0],
    pub _bitfield_1: __BindgenBitfieldUnit<[u8; 4usize]>,
    pub Rtt: u32,
    pub MinRtt: u32,
    pub MaxRtt: u32,
    pub TimingStart: u64,
    pub TimingInitialFlightEnd: u64,
    pub TimingHandshakeFlightEnd: u64,
    pub HandshakeClientFlight1Bytes: u32,
    pub HandshakeServerFlight1Bytes: u32,
    pub HandshakeClientFlight2Bytes: u32,
    pub SendPathMtu: u16,
    pub SendTotalPackets: u64,
    pub SendRetransmittablePackets: u64,
    pub SendSuspectedLostPackets: u64,
    pub SendSpuriousLostPackets: u64,
    pub SendTotalBytes: u64,
    pub SendTotalStreamBytes: u64,
    pub SendCongestionCount: u32,
    pub SendPersistentCongestionCount: u32,
    pub RecvTotalPackets: u64,
    pub RecvReorderedPackets: u64,
    pub RecvDroppedPackets: u64,
    pub RecvDuplicatePackets: u64,
    pub RecvTotalBytes: u64,
    pub RecvTotalStreamBytes: u64,
    pub RecvDecryptionFailures: u64,
    pub RecvValidAckFrames: u64,
    pub KeyUpdateCount: u32,
    pub SendCongestionWindow: u32,
    pub DestCidUpdateCount: u32,
    pub SendEcnCongestionCount: u32,
    pub HandshakeHopLimitTTL: u8,
    pub RttVariance: u32,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_STATISTICS_V2"][::std::mem::size_of::<QUIC_STATISTICS_V2>() - 208usize];
    ["Alignment of QUIC_STATISTICS_V2"][::std::mem::align_of::<QUIC_STATISTICS_V2>() - 8usize];
    ["Offset of field: QUIC_STATISTICS_V2::CorrelationId"]
        [::std::mem::offset_of!(QUIC_STATISTICS_V2, CorrelationId) - 0usize];
    ["Offset of field: QUIC_STATISTICS_V2::Rtt"]
        [::std::mem::offset_of!(QUIC_STATISTICS_V2, Rtt) - 12usize];
    ["Offset of field: QUIC_STATISTICS_V2::MinRtt"]
        [::std::mem::offset_of!(QUIC_STATISTICS_V2, MinRtt) - 16usize];
    ["Offset of field: QUIC_STATISTICS_V2::MaxRtt"]
        [::std::mem::offset_of!(QUIC_STATISTICS_V2, MaxRtt) - 20usize];
    ["Offset of field: QUIC_STATISTICS_V2::TimingStart"]
        [::std::mem::offset_of!(QUIC_STATISTICS_V2, TimingStart) - 24usize];
    ["Offset of field: QUIC_STATISTICS_V2::TimingInitialFlightEnd"]
        [::std::mem::offset_of!(QUIC_STATISTICS_V2, TimingInitialFlightEnd) - 32usize];
    ["Offset of field: QUIC_STATISTICS_V2::TimingHandshakeFlightEnd"]
        [::std::mem::offset_of!(QUIC_STATISTICS_V2, TimingHandshakeFlightEnd) - 40usize];
    ["Offset of field: QUIC_STATISTICS_V2::HandshakeClientFlight1Bytes"]
        [::std::mem::offset_of!(QUIC_STATISTICS_V2, HandshakeClientFlight1Bytes) - 48usize];
    ["Offset of field: QUIC_STATISTICS_V2::HandshakeServerFlight1Bytes"]
        [::std::mem::offset_of!(QUIC_STATISTICS_V2, HandshakeServerFlight1Bytes) - 52usize];
    ["Offset of field: QUIC_STATISTICS_V2::HandshakeClientFlight2Bytes"]
        [::std::mem::offset_of!(QUIC_STATISTICS_V2, HandshakeClientFlight2Bytes) - 56usize];
    ["Offset of field: QUIC_STATISTICS_V2::SendPathMtu"]
        [::std::mem::offset_of!(QUIC_STATISTICS_V2, SendPathMtu) - 60usize];
    ["Offset of field: QUIC_STATISTICS_V2::SendTotalPackets"]
        [::std::mem::offset_of!(QUIC_STATISTICS_V2, SendTotalPackets) - 64usize];
    ["Offset of field: QUIC_STATISTICS_V2::SendRetransmittablePackets"]
        [::std::mem::offset_of!(QUIC_STATISTICS_V2, SendRetransmittablePackets) - 72usize];
    ["Offset of field: QUIC_STATISTICS_V2::SendSuspectedLostPackets"]
        [::std::mem::offset_of!(QUIC_STATISTICS_V2, SendSuspectedLostPackets) - 80usize];
    ["Offset of field: QUIC_STATISTICS_V2::SendSpuriousLostPackets"]
        [::std::mem::offset_of!(QUIC_STATISTICS_V2, SendSpuriousLostPackets) - 88usize];
    ["Offset of field: QUIC_STATISTICS_V2::SendTotalBytes"]
        [::std::mem::offset_of!(QUIC_STATISTICS_V2, SendTotalBytes) - 96usize];
    ["Offset of field: QUIC_STATISTICS_V2::SendTotalStreamBytes"]
        [::std::mem::offset_of!(QUIC_STATISTICS_V2, SendTotalStreamBytes) - 104usize];
    ["Offset of field: QUIC_STATISTICS_V2::SendCongestionCount"]
        [::std::mem::offset_of!(QUIC_STATISTICS_V2, SendCongestionCount) - 112usize];
    ["Offset of field: QUIC_STATISTICS_V2::SendPersistentCongestionCount"]
        [::std::mem::offset_of!(QUIC_STATISTICS_V2, SendPersistentCongestionCount) - 116usize];
    ["Offset of field: QUIC_STATISTICS_V2::RecvTotalPackets"]
        [::std::mem::offset_of!(QUIC_STATISTICS_V2, RecvTotalPackets) - 120usize];
    ["Offset of field: QUIC_STATISTICS_V2::RecvReorderedPackets"]
        [::std::mem::offset_of!(QUIC_STATISTICS_V2, RecvReorderedPackets) - 128usize];
    ["Offset of field: QUIC_STATISTICS_V2::RecvDroppedPackets"]
        [::std::mem::offset_of!(QUIC_STATISTICS_V2, RecvDroppedPackets) - 136usize];
    ["Offset of field: QUIC_STATISTICS_V2::RecvDuplicatePackets"]
        [::std::mem::offset_of!(QUIC_STATISTICS_V2, RecvDuplicatePackets) - 144usize];
    ["Offset of field: QUIC_STATISTICS_V2::RecvTotalBytes"]
        [::std::mem::offset_of!(QUIC_STATISTICS_V2, RecvTotalBytes) - 152usize];
    ["Offset of field: QUIC_STATISTICS_V2::RecvTotalStreamBytes"]
        [::std::mem::offset_of!(QUIC_STATISTICS_V2, RecvTotalStreamBytes) - 160usize];
    ["Offset of field: QUIC_STATISTICS_V2::RecvDecryptionFailures"]
        [::std::mem::offset_of!(QUIC_STATISTICS_V2, RecvDecryptionFailures) - 168usize];
    ["Offset of field: QUIC_STATISTICS_V2::RecvValidAckFrames"]
        [::std::mem::offset_of!(QUIC_STATISTICS_V2, RecvValidAckFrames) - 176usize];
    ["Offset of field: QUIC_STATISTICS_V2::KeyUpdateCount"]
        [::std::mem::offset_of!(QUIC_STATISTICS_V2, KeyUpdateCount) - 184usize];
    ["Offset of field: QUIC_STATISTICS_V2::SendCongestionWindow"]
        [::std::mem::offset_of!(QUIC_STATISTICS_V2, SendCongestionWindow) - 188usize];
    ["Offset of field: QUIC_STATISTICS_V2::DestCidUpdateCount"]
        [::std::mem::offset_of!(QUIC_STATISTICS_V2, DestCidUpdateCount) - 192usize];
    ["Offset of field: QUIC_STATISTICS_V2::SendEcnCongestionCount"]
        [::std::mem::offset_of!(QUIC_STATISTICS_V2, SendEcnCongestionCount) - 196usize];
    ["Offset of field: QUIC_STATISTICS_V2::HandshakeHopLimitTTL"]
        [::std::mem::offset_of!(QUIC_STATISTICS_V2, HandshakeHopLimitTTL) - 200usize];
    ["Offset of field: QUIC_STATISTICS_V2::RttVariance"]
        [::std::mem::offset_of!(QUIC_STATISTICS_V2, RttVariance) - 204usize];
};
impl QUIC_STATISTICS_V2 {
    #[inline]
    pub fn VersionNegotiation(&self) -> u32 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(0usize, 1u8) as u32) }
    }
    #[inline]
    pub fn set_VersionNegotiation(&mut self, val: u32) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            self._bitfield_1.set(0usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn VersionNegotiation_raw(this: *const Self) -> u32 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                0usize,
                1u8,
            ) as u32)
        }
    }
    #[inline]
    pub unsafe fn set_VersionNegotiation_raw(this: *mut Self, val: u32) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 4usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                0usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn StatelessRetry(&self) -> u32 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(1usize, 1u8) as u32) }
    }
    #[inline]
    pub fn set_StatelessRetry(&mut self, val: u32) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            self._bitfield_1.set(1usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn StatelessRetry_raw(this: *const Self) -> u32 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                1usize,
                1u8,
            ) as u32)
        }
    }
    #[inline]
    pub unsafe fn set_StatelessRetry_raw(this: *mut Self, val: u32) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 4usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                1usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn ResumptionAttempted(&self) -> u32 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(2usize, 1u8) as u32) }
    }
    #[inline]
    pub fn set_ResumptionAttempted(&mut self, val: u32) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            self._bitfield_1.set(2usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn ResumptionAttempted_raw(this: *const Self) -> u32 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                2usize,
                1u8,
            ) as u32)
        }
    }
    #[inline]
    pub unsafe fn set_ResumptionAttempted_raw(this: *mut Self, val: u32) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 4usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                2usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn ResumptionSucceeded(&self) -> u32 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(3usize, 1u8) as u32) }
    }
    #[inline]
    pub fn set_ResumptionSucceeded(&mut self, val: u32) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            self._bitfield_1.set(3usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn ResumptionSucceeded_raw(this: *const Self) -> u32 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                3usize,
                1u8,
            ) as u32)
        }
    }
    #[inline]
    pub unsafe fn set_ResumptionSucceeded_raw(this: *mut Self, val: u32) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 4usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                3usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn GreaseBitNegotiated(&self) -> u32 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(4usize, 1u8) as u32) }
    }
    #[inline]
    pub fn set_GreaseBitNegotiated(&mut self, val: u32) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            self._bitfield_1.set(4usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn GreaseBitNegotiated_raw(this: *const Self) -> u32 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                4usize,
                1u8,
            ) as u32)
        }
    }
    #[inline]
    pub unsafe fn set_GreaseBitNegotiated_raw(this: *mut Self, val: u32) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 4usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                4usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn EcnCapable(&self) -> u32 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(5usize, 1u8) as u32) }
    }
    #[inline]
    pub fn set_EcnCapable(&mut self, val: u32) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            self._bitfield_1.set(5usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn EcnCapable_raw(this: *const Self) -> u32 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                5usize,
                1u8,
            ) as u32)
        }
    }
    #[inline]
    pub unsafe fn set_EcnCapable_raw(this: *mut Self, val: u32) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 4usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                5usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn EncryptionOffloaded(&self) -> u32 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(6usize, 1u8) as u32) }
    }
    #[inline]
    pub fn set_EncryptionOffloaded(&mut self, val: u32) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            self._bitfield_1.set(6usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn EncryptionOffloaded_raw(this: *const Self) -> u32 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                6usize,
                1u8,
            ) as u32)
        }
    }
    #[inline]
    pub unsafe fn set_EncryptionOffloaded_raw(this: *mut Self, val: u32) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 4usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                6usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn RESERVED(&self) -> u32 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(7usize, 25u8) as u32) }
    }
    #[inline]
    pub fn set_RESERVED(&mut self, val: u32) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            self._bitfield_1.set(7usize, 25u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn RESERVED_raw(this: *const Self) -> u32 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                7usize,
                25u8,
            ) as u32)
        }
    }
    #[inline]
    pub unsafe fn set_RESERVED_raw(this: *mut Self, val: u32) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 4usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                7usize,
                25u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn new_bitfield_1(
        VersionNegotiation: u32,
        StatelessRetry: u32,
        ResumptionAttempted: u32,
        ResumptionSucceeded: u32,
        GreaseBitNegotiated: u32,
        EcnCapable: u32,
        EncryptionOffloaded: u32,
        RESERVED: u32,
    ) -> __BindgenBitfieldUnit<[u8; 4usize]> {
        let mut __bindgen_bitfield_unit: __BindgenBitfieldUnit<[u8; 4usize]> = Default::default();
        __bindgen_bitfield_unit.set(0usize, 1u8, {
            let VersionNegotiation: u32 = unsafe { ::std::mem::transmute(VersionNegotiation) };
            VersionNegotiation as u64
        });
        __bindgen_bitfield_unit.set(1usize, 1u8, {
            let StatelessRetry: u32 = unsafe { ::std::mem::transmute(StatelessRetry) };
            StatelessRetry as u64
        });
        __bindgen_bitfield_unit.set(2usize, 1u8, {
            let ResumptionAttempted: u32 = unsafe { ::std::mem::transmute(ResumptionAttempted) };
            ResumptionAttempted as u64
        });
        __bindgen_bitfield_unit.set(3usize, 1u8, {
            let ResumptionSucceeded: u32 = unsafe { ::std::mem::transmute(ResumptionSucceeded) };
            ResumptionSucceeded as u64
        });
        __bindgen_bitfield_unit.set(4usize, 1u8, {
            let GreaseBitNegotiated: u32 = unsafe { ::std::mem::transmute(GreaseBitNegotiated) };
            GreaseBitNegotiated as u64
        });
        __bindgen_bitfield_unit.set(5usize, 1u8, {
            let EcnCapable: u32 = unsafe { ::std::mem::transmute(EcnCapable) };
            EcnCapable as u64
        });
        __bindgen_bitfield_unit.set(6usize, 1u8, {
            let EncryptionOffloaded: u32 = unsafe { ::std::mem::transmute(EncryptionOffloaded) };
            EncryptionOffloaded as u64
        });
        __bindgen_bitfield_unit.set(7usize, 25u8, {
            let RESERVED: u32 = unsafe { ::std::mem::transmute(RESERVED) };
            RESERVED as u64
        });
        __bindgen_bitfield_unit
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_NETWORK_STATISTICS {
    pub BytesInFlight: u32,
    pub PostedBytes: u64,
    pub IdealBytes: u64,
    pub SmoothedRTT: u64,
    pub CongestionWindow: u32,
    pub Bandwidth: u64,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_NETWORK_STATISTICS"][::std::mem::size_of::<QUIC_NETWORK_STATISTICS>() - 48usize];
    ["Alignment of QUIC_NETWORK_STATISTICS"]
        [::std::mem::align_of::<QUIC_NETWORK_STATISTICS>() - 8usize];
    ["Offset of field: QUIC_NETWORK_STATISTICS::BytesInFlight"]
        [::std::mem::offset_of!(QUIC_NETWORK_STATISTICS, BytesInFlight) - 0usize];
    ["Offset of field: QUIC_NETWORK_STATISTICS::PostedBytes"]
        [::std::mem::offset_of!(QUIC_NETWORK_STATISTICS, PostedBytes) - 8usize];
    ["Offset of field: QUIC_NETWORK_STATISTICS::IdealBytes"]
        [::std::mem::offset_of!(QUIC_NETWORK_STATISTICS, IdealBytes) - 16usize];
    ["Offset of field: QUIC_NETWORK_STATISTICS::SmoothedRTT"]
        [::std::mem::offset_of!(QUIC_NETWORK_STATISTICS, SmoothedRTT) - 24usize];
    ["Offset of field: QUIC_NETWORK_STATISTICS::CongestionWindow"]
        [::std::mem::offset_of!(QUIC_NETWORK_STATISTICS, CongestionWindow) - 32usize];
    ["Offset of field: QUIC_NETWORK_STATISTICS::Bandwidth"]
        [::std::mem::offset_of!(QUIC_NETWORK_STATISTICS, Bandwidth) - 40usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_LISTENER_STATISTICS {
    pub TotalAcceptedConnections: u64,
    pub TotalRejectedConnections: u64,
    pub BindingRecvDroppedPackets: u64,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_LISTENER_STATISTICS"]
        [::std::mem::size_of::<QUIC_LISTENER_STATISTICS>() - 24usize];
    ["Alignment of QUIC_LISTENER_STATISTICS"]
        [::std::mem::align_of::<QUIC_LISTENER_STATISTICS>() - 8usize];
    ["Offset of field: QUIC_LISTENER_STATISTICS::TotalAcceptedConnections"]
        [::std::mem::offset_of!(QUIC_LISTENER_STATISTICS, TotalAcceptedConnections) - 0usize];
    ["Offset of field: QUIC_LISTENER_STATISTICS::TotalRejectedConnections"]
        [::std::mem::offset_of!(QUIC_LISTENER_STATISTICS, TotalRejectedConnections) - 8usize];
    ["Offset of field: QUIC_LISTENER_STATISTICS::BindingRecvDroppedPackets"]
        [::std::mem::offset_of!(QUIC_LISTENER_STATISTICS, BindingRecvDroppedPackets) - 16usize];
};
pub const QUIC_PERFORMANCE_COUNTERS_QUIC_PERF_COUNTER_CONN_CREATED: QUIC_PERFORMANCE_COUNTERS = 0;
pub const QUIC_PERFORMANCE_COUNTERS_QUIC_PERF_COUNTER_CONN_HANDSHAKE_FAIL:
    QUIC_PERFORMANCE_COUNTERS = 1;
pub const QUIC_PERFORMANCE_COUNTERS_QUIC_PERF_COUNTER_CONN_APP_REJECT: QUIC_PERFORMANCE_COUNTERS =
    2;
pub const QUIC_PERFORMANCE_COUNTERS_QUIC_PERF_COUNTER_CONN_RESUMED: QUIC_PERFORMANCE_COUNTERS = 3;
pub const QUIC_PERFORMANCE_COUNTERS_QUIC_PERF_COUNTER_CONN_ACTIVE: QUIC_PERFORMANCE_COUNTERS = 4;
pub const QUIC_PERFORMANCE_COUNTERS_QUIC_PERF_COUNTER_CONN_CONNECTED: QUIC_PERFORMANCE_COUNTERS = 5;
pub const QUIC_PERFORMANCE_COUNTERS_QUIC_PERF_COUNTER_CONN_PROTOCOL_ERRORS:
    QUIC_PERFORMANCE_COUNTERS = 6;
pub const QUIC_PERFORMANCE_COUNTERS_QUIC_PERF_COUNTER_CONN_NO_ALPN: QUIC_PERFORMANCE_COUNTERS = 7;
pub const QUIC_PERFORMANCE_COUNTERS_QUIC_PERF_COUNTER_STRM_ACTIVE: QUIC_PERFORMANCE_COUNTERS = 8;
pub const QUIC_PERFORMANCE_COUNTERS_QUIC_PERF_COUNTER_PKTS_SUSPECTED_LOST:
    QUIC_PERFORMANCE_COUNTERS = 9;
pub const QUIC_PERFORMANCE_COUNTERS_QUIC_PERF_COUNTER_PKTS_DROPPED: QUIC_PERFORMANCE_COUNTERS = 10;
pub const QUIC_PERFORMANCE_COUNTERS_QUIC_PERF_COUNTER_PKTS_DECRYPTION_FAIL:
    QUIC_PERFORMANCE_COUNTERS = 11;
pub const QUIC_PERFORMANCE_COUNTERS_QUIC_PERF_COUNTER_UDP_RECV: QUIC_PERFORMANCE_COUNTERS = 12;
pub const QUIC_PERFORMANCE_COUNTERS_QUIC_PERF_COUNTER_UDP_SEND: QUIC_PERFORMANCE_COUNTERS = 13;
pub const QUIC_PERFORMANCE_COUNTERS_QUIC_PERF_COUNTER_UDP_RECV_BYTES: QUIC_PERFORMANCE_COUNTERS =
    14;
pub const QUIC_PERFORMANCE_COUNTERS_QUIC_PERF_COUNTER_UDP_SEND_BYTES: QUIC_PERFORMANCE_COUNTERS =
    15;
pub const QUIC_PERFORMANCE_COUNTERS_QUIC_PERF_COUNTER_UDP_RECV_EVENTS: QUIC_PERFORMANCE_COUNTERS =
    16;
pub const QUIC_PERFORMANCE_COUNTERS_QUIC_PERF_COUNTER_UDP_SEND_CALLS: QUIC_PERFORMANCE_COUNTERS =
    17;
pub const QUIC_PERFORMANCE_COUNTERS_QUIC_PERF_COUNTER_APP_SEND_BYTES: QUIC_PERFORMANCE_COUNTERS =
    18;
pub const QUIC_PERFORMANCE_COUNTERS_QUIC_PERF_COUNTER_APP_RECV_BYTES: QUIC_PERFORMANCE_COUNTERS =
    19;
pub const QUIC_PERFORMANCE_COUNTERS_QUIC_PERF_COUNTER_CONN_QUEUE_DEPTH: QUIC_PERFORMANCE_COUNTERS =
    20;
pub const QUIC_PERFORMANCE_COUNTERS_QUIC_PERF_COUNTER_CONN_OPER_QUEUE_DEPTH:
    QUIC_PERFORMANCE_COUNTERS = 21;
pub const QUIC_PERFORMANCE_COUNTERS_QUIC_PERF_COUNTER_CONN_OPER_QUEUED: QUIC_PERFORMANCE_COUNTERS =
    22;
pub const QUIC_PERFORMANCE_COUNTERS_QUIC_PERF_COUNTER_CONN_OPER_COMPLETED:
    QUIC_PERFORMANCE_COUNTERS = 23;
pub const QUIC_PERFORMANCE_COUNTERS_QUIC_PERF_COUNTER_WORK_OPER_QUEUE_DEPTH:
    QUIC_PERFORMANCE_COUNTERS = 24;
pub const QUIC_PERFORMANCE_COUNTERS_QUIC_PERF_COUNTER_WORK_OPER_QUEUED: QUIC_PERFORMANCE_COUNTERS =
    25;
pub const QUIC_PERFORMANCE_COUNTERS_QUIC_PERF_COUNTER_WORK_OPER_COMPLETED:
    QUIC_PERFORMANCE_COUNTERS = 26;
pub const QUIC_PERFORMANCE_COUNTERS_QUIC_PERF_COUNTER_PATH_VALIDATED: QUIC_PERFORMANCE_COUNTERS =
    27;
pub const QUIC_PERFORMANCE_COUNTERS_QUIC_PERF_COUNTER_PATH_FAILURE: QUIC_PERFORMANCE_COUNTERS = 28;
pub const QUIC_PERFORMANCE_COUNTERS_QUIC_PERF_COUNTER_SEND_STATELESS_RESET:
    QUIC_PERFORMANCE_COUNTERS = 29;
pub const QUIC_PERFORMANCE_COUNTERS_QUIC_PERF_COUNTER_SEND_STATELESS_RETRY:
    QUIC_PERFORMANCE_COUNTERS = 30;
pub const QUIC_PERFORMANCE_COUNTERS_QUIC_PERF_COUNTER_CONN_LOAD_REJECT: QUIC_PERFORMANCE_COUNTERS =
    31;
pub const QUIC_PERFORMANCE_COUNTERS_QUIC_PERF_COUNTER_MAX: QUIC_PERFORMANCE_COUNTERS = 32;
pub type QUIC_PERFORMANCE_COUNTERS = ::std::os::raw::c_int;
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_VERSION_SETTINGS {
    pub AcceptableVersions: *const u32,
    pub OfferedVersions: *const u32,
    pub FullyDeployedVersions: *const u32,
    pub AcceptableVersionsLength: u32,
    pub OfferedVersionsLength: u32,
    pub FullyDeployedVersionsLength: u32,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_VERSION_SETTINGS"][::std::mem::size_of::<QUIC_VERSION_SETTINGS>() - 40usize];
    ["Alignment of QUIC_VERSION_SETTINGS"]
        [::std::mem::align_of::<QUIC_VERSION_SETTINGS>() - 8usize];
    ["Offset of field: QUIC_VERSION_SETTINGS::AcceptableVersions"]
        [::std::mem::offset_of!(QUIC_VERSION_SETTINGS, AcceptableVersions) - 0usize];
    ["Offset of field: QUIC_VERSION_SETTINGS::OfferedVersions"]
        [::std::mem::offset_of!(QUIC_VERSION_SETTINGS, OfferedVersions) - 8usize];
    ["Offset of field: QUIC_VERSION_SETTINGS::FullyDeployedVersions"]
        [::std::mem::offset_of!(QUIC_VERSION_SETTINGS, FullyDeployedVersions) - 16usize];
    ["Offset of field: QUIC_VERSION_SETTINGS::AcceptableVersionsLength"]
        [::std::mem::offset_of!(QUIC_VERSION_SETTINGS, AcceptableVersionsLength) - 24usize];
    ["Offset of field: QUIC_VERSION_SETTINGS::OfferedVersionsLength"]
        [::std::mem::offset_of!(QUIC_VERSION_SETTINGS, OfferedVersionsLength) - 28usize];
    ["Offset of field: QUIC_VERSION_SETTINGS::FullyDeployedVersionsLength"]
        [::std::mem::offset_of!(QUIC_VERSION_SETTINGS, FullyDeployedVersionsLength) - 32usize];
};
#[repr(C)]
#[derive(Copy, Clone)]
pub struct QUIC_GLOBAL_SETTINGS {
    pub __bindgen_anon_1: QUIC_GLOBAL_SETTINGS__bindgen_ty_1,
    pub RetryMemoryLimit: u16,
    pub LoadBalancingMode: u16,
    pub FixedServerID: u32,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union QUIC_GLOBAL_SETTINGS__bindgen_ty_1 {
    pub IsSetFlags: u64,
    pub IsSet: QUIC_GLOBAL_SETTINGS__bindgen_ty_1__bindgen_ty_1,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_GLOBAL_SETTINGS__bindgen_ty_1__bindgen_ty_1 {
    pub _bitfield_align_1: [u64; 0],
    pub _bitfield_1: __BindgenBitfieldUnit<[u8; 8usize]>,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_GLOBAL_SETTINGS__bindgen_ty_1__bindgen_ty_1"]
        [::std::mem::size_of::<QUIC_GLOBAL_SETTINGS__bindgen_ty_1__bindgen_ty_1>() - 8usize];
    ["Alignment of QUIC_GLOBAL_SETTINGS__bindgen_ty_1__bindgen_ty_1"]
        [::std::mem::align_of::<QUIC_GLOBAL_SETTINGS__bindgen_ty_1__bindgen_ty_1>() - 8usize];
};
impl QUIC_GLOBAL_SETTINGS__bindgen_ty_1__bindgen_ty_1 {
    #[inline]
    pub fn RetryMemoryLimit(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(0usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_RetryMemoryLimit(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(0usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn RetryMemoryLimit_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                0usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_RetryMemoryLimit_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                0usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn LoadBalancingMode(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(1usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_LoadBalancingMode(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(1usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn LoadBalancingMode_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                1usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_LoadBalancingMode_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                1usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn FixedServerID(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(2usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_FixedServerID(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(2usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn FixedServerID_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                2usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_FixedServerID_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                2usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn RESERVED(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(3usize, 61u8) as u64) }
    }
    #[inline]
    pub fn set_RESERVED(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(3usize, 61u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn RESERVED_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                3usize,
                61u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_RESERVED_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                3usize,
                61u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn new_bitfield_1(
        RetryMemoryLimit: u64,
        LoadBalancingMode: u64,
        FixedServerID: u64,
        RESERVED: u64,
    ) -> __BindgenBitfieldUnit<[u8; 8usize]> {
        let mut __bindgen_bitfield_unit: __BindgenBitfieldUnit<[u8; 8usize]> = Default::default();
        __bindgen_bitfield_unit.set(0usize, 1u8, {
            let RetryMemoryLimit: u64 = unsafe { ::std::mem::transmute(RetryMemoryLimit) };
            RetryMemoryLimit as u64
        });
        __bindgen_bitfield_unit.set(1usize, 1u8, {
            let LoadBalancingMode: u64 = unsafe { ::std::mem::transmute(LoadBalancingMode) };
            LoadBalancingMode as u64
        });
        __bindgen_bitfield_unit.set(2usize, 1u8, {
            let FixedServerID: u64 = unsafe { ::std::mem::transmute(FixedServerID) };
            FixedServerID as u64
        });
        __bindgen_bitfield_unit.set(3usize, 61u8, {
            let RESERVED: u64 = unsafe { ::std::mem::transmute(RESERVED) };
            RESERVED as u64
        });
        __bindgen_bitfield_unit
    }
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_GLOBAL_SETTINGS__bindgen_ty_1"]
        [::std::mem::size_of::<QUIC_GLOBAL_SETTINGS__bindgen_ty_1>() - 8usize];
    ["Alignment of QUIC_GLOBAL_SETTINGS__bindgen_ty_1"]
        [::std::mem::align_of::<QUIC_GLOBAL_SETTINGS__bindgen_ty_1>() - 8usize];
    ["Offset of field: QUIC_GLOBAL_SETTINGS__bindgen_ty_1::IsSetFlags"]
        [::std::mem::offset_of!(QUIC_GLOBAL_SETTINGS__bindgen_ty_1, IsSetFlags) - 0usize];
    ["Offset of field: QUIC_GLOBAL_SETTINGS__bindgen_ty_1::IsSet"]
        [::std::mem::offset_of!(QUIC_GLOBAL_SETTINGS__bindgen_ty_1, IsSet) - 0usize];
};
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_GLOBAL_SETTINGS"][::std::mem::size_of::<QUIC_GLOBAL_SETTINGS>() - 16usize];
    ["Alignment of QUIC_GLOBAL_SETTINGS"][::std::mem::align_of::<QUIC_GLOBAL_SETTINGS>() - 8usize];
    ["Offset of field: QUIC_GLOBAL_SETTINGS::RetryMemoryLimit"]
        [::std::mem::offset_of!(QUIC_GLOBAL_SETTINGS, RetryMemoryLimit) - 8usize];
    ["Offset of field: QUIC_GLOBAL_SETTINGS::LoadBalancingMode"]
        [::std::mem::offset_of!(QUIC_GLOBAL_SETTINGS, LoadBalancingMode) - 10usize];
    ["Offset of field: QUIC_GLOBAL_SETTINGS::FixedServerID"]
        [::std::mem::offset_of!(QUIC_GLOBAL_SETTINGS, FixedServerID) - 12usize];
};
#[repr(C)]
#[derive(Copy, Clone)]
pub struct QUIC_SETTINGS {
    pub __bindgen_anon_1: QUIC_SETTINGS__bindgen_ty_1,
    pub MaxBytesPerKey: u64,
    pub HandshakeIdleTimeoutMs: u64,
    pub IdleTimeoutMs: u64,
    pub MtuDiscoverySearchCompleteTimeoutUs: u64,
    pub TlsClientMaxSendBuffer: u32,
    pub TlsServerMaxSendBuffer: u32,
    pub StreamRecvWindowDefault: u32,
    pub StreamRecvBufferDefault: u32,
    pub ConnFlowControlWindow: u32,
    pub MaxWorkerQueueDelayUs: u32,
    pub MaxStatelessOperations: u32,
    pub InitialWindowPackets: u32,
    pub SendIdleTimeoutMs: u32,
    pub InitialRttMs: u32,
    pub MaxAckDelayMs: u32,
    pub DisconnectTimeoutMs: u32,
    pub KeepAliveIntervalMs: u32,
    pub CongestionControlAlgorithm: u16,
    pub PeerBidiStreamCount: u16,
    pub PeerUnidiStreamCount: u16,
    pub MaxBindingStatelessOperations: u16,
    pub StatelessOperationExpirationMs: u16,
    pub MinimumMtu: u16,
    pub MaximumMtu: u16,
    pub _bitfield_align_1: [u8; 0],
    pub _bitfield_1: __BindgenBitfieldUnit<[u8; 1usize]>,
    pub MaxOperationsPerDrain: u8,
    pub MtuDiscoveryMissingProbeCount: u8,
    pub DestCidUpdateIdleTimeoutMs: u32,
    pub __bindgen_anon_2: QUIC_SETTINGS__bindgen_ty_2,
    pub StreamRecvWindowBidiLocalDefault: u32,
    pub StreamRecvWindowBidiRemoteDefault: u32,
    pub StreamRecvWindowUnidiDefault: u32,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union QUIC_SETTINGS__bindgen_ty_1 {
    pub IsSetFlags: u64,
    pub IsSet: QUIC_SETTINGS__bindgen_ty_1__bindgen_ty_1,
}
#[repr(C)]
#[repr(align(8))]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_SETTINGS__bindgen_ty_1__bindgen_ty_1 {
    pub _bitfield_align_1: [u32; 0],
    pub _bitfield_1: __BindgenBitfieldUnit<[u8; 8usize]>,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_SETTINGS__bindgen_ty_1__bindgen_ty_1"]
        [::std::mem::size_of::<QUIC_SETTINGS__bindgen_ty_1__bindgen_ty_1>() - 8usize];
    ["Alignment of QUIC_SETTINGS__bindgen_ty_1__bindgen_ty_1"]
        [::std::mem::align_of::<QUIC_SETTINGS__bindgen_ty_1__bindgen_ty_1>() - 8usize];
};
impl QUIC_SETTINGS__bindgen_ty_1__bindgen_ty_1 {
    #[inline]
    pub fn MaxBytesPerKey(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(0usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_MaxBytesPerKey(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(0usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn MaxBytesPerKey_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                0usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_MaxBytesPerKey_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                0usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn HandshakeIdleTimeoutMs(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(1usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_HandshakeIdleTimeoutMs(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(1usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn HandshakeIdleTimeoutMs_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                1usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_HandshakeIdleTimeoutMs_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                1usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn IdleTimeoutMs(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(2usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_IdleTimeoutMs(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(2usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn IdleTimeoutMs_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                2usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_IdleTimeoutMs_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                2usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn MtuDiscoverySearchCompleteTimeoutUs(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(3usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_MtuDiscoverySearchCompleteTimeoutUs(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(3usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn MtuDiscoverySearchCompleteTimeoutUs_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                3usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_MtuDiscoverySearchCompleteTimeoutUs_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                3usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn TlsClientMaxSendBuffer(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(4usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_TlsClientMaxSendBuffer(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(4usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn TlsClientMaxSendBuffer_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                4usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_TlsClientMaxSendBuffer_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                4usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn TlsServerMaxSendBuffer(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(5usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_TlsServerMaxSendBuffer(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(5usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn TlsServerMaxSendBuffer_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                5usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_TlsServerMaxSendBuffer_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                5usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn StreamRecvWindowDefault(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(6usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_StreamRecvWindowDefault(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(6usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn StreamRecvWindowDefault_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                6usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_StreamRecvWindowDefault_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                6usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn StreamRecvBufferDefault(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(7usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_StreamRecvBufferDefault(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(7usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn StreamRecvBufferDefault_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                7usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_StreamRecvBufferDefault_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                7usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn ConnFlowControlWindow(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(8usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_ConnFlowControlWindow(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(8usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn ConnFlowControlWindow_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                8usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_ConnFlowControlWindow_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                8usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn MaxWorkerQueueDelayUs(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(9usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_MaxWorkerQueueDelayUs(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(9usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn MaxWorkerQueueDelayUs_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                9usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_MaxWorkerQueueDelayUs_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                9usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn MaxStatelessOperations(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(10usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_MaxStatelessOperations(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(10usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn MaxStatelessOperations_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                10usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_MaxStatelessOperations_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                10usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn InitialWindowPackets(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(11usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_InitialWindowPackets(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(11usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn InitialWindowPackets_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                11usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_InitialWindowPackets_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                11usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn SendIdleTimeoutMs(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(12usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_SendIdleTimeoutMs(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(12usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn SendIdleTimeoutMs_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                12usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_SendIdleTimeoutMs_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                12usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn InitialRttMs(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(13usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_InitialRttMs(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(13usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn InitialRttMs_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                13usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_InitialRttMs_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                13usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn MaxAckDelayMs(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(14usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_MaxAckDelayMs(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(14usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn MaxAckDelayMs_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                14usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_MaxAckDelayMs_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                14usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn DisconnectTimeoutMs(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(15usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_DisconnectTimeoutMs(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(15usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn DisconnectTimeoutMs_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                15usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_DisconnectTimeoutMs_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                15usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn KeepAliveIntervalMs(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(16usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_KeepAliveIntervalMs(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(16usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn KeepAliveIntervalMs_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                16usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_KeepAliveIntervalMs_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                16usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn CongestionControlAlgorithm(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(17usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_CongestionControlAlgorithm(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(17usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn CongestionControlAlgorithm_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                17usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_CongestionControlAlgorithm_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                17usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn PeerBidiStreamCount(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(18usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_PeerBidiStreamCount(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(18usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn PeerBidiStreamCount_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                18usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_PeerBidiStreamCount_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                18usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn PeerUnidiStreamCount(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(19usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_PeerUnidiStreamCount(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(19usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn PeerUnidiStreamCount_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                19usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_PeerUnidiStreamCount_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                19usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn MaxBindingStatelessOperations(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(20usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_MaxBindingStatelessOperations(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(20usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn MaxBindingStatelessOperations_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                20usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_MaxBindingStatelessOperations_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                20usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn StatelessOperationExpirationMs(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(21usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_StatelessOperationExpirationMs(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(21usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn StatelessOperationExpirationMs_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                21usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_StatelessOperationExpirationMs_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                21usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn MinimumMtu(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(22usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_MinimumMtu(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(22usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn MinimumMtu_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                22usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_MinimumMtu_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                22usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn MaximumMtu(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(23usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_MaximumMtu(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(23usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn MaximumMtu_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                23usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_MaximumMtu_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                23usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn SendBufferingEnabled(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(24usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_SendBufferingEnabled(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(24usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn SendBufferingEnabled_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                24usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_SendBufferingEnabled_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                24usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn PacingEnabled(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(25usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_PacingEnabled(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(25usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn PacingEnabled_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                25usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_PacingEnabled_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                25usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn MigrationEnabled(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(26usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_MigrationEnabled(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(26usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn MigrationEnabled_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                26usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_MigrationEnabled_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                26usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn DatagramReceiveEnabled(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(27usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_DatagramReceiveEnabled(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(27usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn DatagramReceiveEnabled_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                27usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_DatagramReceiveEnabled_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                27usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn ServerResumptionLevel(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(28usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_ServerResumptionLevel(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(28usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn ServerResumptionLevel_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                28usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_ServerResumptionLevel_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                28usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn MaxOperationsPerDrain(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(29usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_MaxOperationsPerDrain(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(29usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn MaxOperationsPerDrain_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                29usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_MaxOperationsPerDrain_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                29usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn MtuDiscoveryMissingProbeCount(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(30usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_MtuDiscoveryMissingProbeCount(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(30usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn MtuDiscoveryMissingProbeCount_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                30usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_MtuDiscoveryMissingProbeCount_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                30usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn DestCidUpdateIdleTimeoutMs(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(31usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_DestCidUpdateIdleTimeoutMs(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(31usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn DestCidUpdateIdleTimeoutMs_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                31usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_DestCidUpdateIdleTimeoutMs_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                31usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn GreaseQuicBitEnabled(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(32usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_GreaseQuicBitEnabled(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(32usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn GreaseQuicBitEnabled_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                32usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_GreaseQuicBitEnabled_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                32usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn EcnEnabled(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(33usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_EcnEnabled(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(33usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn EcnEnabled_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                33usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_EcnEnabled_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                33usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn HyStartEnabled(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(34usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_HyStartEnabled(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(34usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn HyStartEnabled_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                34usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_HyStartEnabled_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                34usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn StreamRecvWindowBidiLocalDefault(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(35usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_StreamRecvWindowBidiLocalDefault(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(35usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn StreamRecvWindowBidiLocalDefault_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                35usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_StreamRecvWindowBidiLocalDefault_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                35usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn StreamRecvWindowBidiRemoteDefault(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(36usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_StreamRecvWindowBidiRemoteDefault(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(36usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn StreamRecvWindowBidiRemoteDefault_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                36usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_StreamRecvWindowBidiRemoteDefault_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                36usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn StreamRecvWindowUnidiDefault(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(37usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_StreamRecvWindowUnidiDefault(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(37usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn StreamRecvWindowUnidiDefault_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                37usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_StreamRecvWindowUnidiDefault_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                37usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn EncryptionOffloadAllowed(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(38usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_EncryptionOffloadAllowed(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(38usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn EncryptionOffloadAllowed_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                38usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_EncryptionOffloadAllowed_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                38usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn ReliableResetEnabled(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(39usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_ReliableResetEnabled(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(39usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn ReliableResetEnabled_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                39usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_ReliableResetEnabled_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                39usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn OneWayDelayEnabled(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(40usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_OneWayDelayEnabled(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(40usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn OneWayDelayEnabled_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                40usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_OneWayDelayEnabled_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                40usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn NetStatsEventEnabled(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(41usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_NetStatsEventEnabled(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(41usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn NetStatsEventEnabled_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                41usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_NetStatsEventEnabled_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                41usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn StreamMultiReceiveEnabled(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(42usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_StreamMultiReceiveEnabled(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(42usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn StreamMultiReceiveEnabled_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                42usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_StreamMultiReceiveEnabled_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                42usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn XdpEnabled(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(43usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_XdpEnabled(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(43usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn XdpEnabled_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                43usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_XdpEnabled_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                43usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn QTIPEnabled(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(44usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_QTIPEnabled(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(44usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn QTIPEnabled_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                44usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_QTIPEnabled_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                44usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn RioEnabled(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(45usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_RioEnabled(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(45usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn RioEnabled_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                45usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_RioEnabled_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                45usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn RESERVED(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(46usize, 18u8) as u64) }
    }
    #[inline]
    pub fn set_RESERVED(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(46usize, 18u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn RESERVED_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                46usize,
                18u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_RESERVED_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                46usize,
                18u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn new_bitfield_1(
        MaxBytesPerKey: u64,
        HandshakeIdleTimeoutMs: u64,
        IdleTimeoutMs: u64,
        MtuDiscoverySearchCompleteTimeoutUs: u64,
        TlsClientMaxSendBuffer: u64,
        TlsServerMaxSendBuffer: u64,
        StreamRecvWindowDefault: u64,
        StreamRecvBufferDefault: u64,
        ConnFlowControlWindow: u64,
        MaxWorkerQueueDelayUs: u64,
        MaxStatelessOperations: u64,
        InitialWindowPackets: u64,
        SendIdleTimeoutMs: u64,
        InitialRttMs: u64,
        MaxAckDelayMs: u64,
        DisconnectTimeoutMs: u64,
        KeepAliveIntervalMs: u64,
        CongestionControlAlgorithm: u64,
        PeerBidiStreamCount: u64,
        PeerUnidiStreamCount: u64,
        MaxBindingStatelessOperations: u64,
        StatelessOperationExpirationMs: u64,
        MinimumMtu: u64,
        MaximumMtu: u64,
        SendBufferingEnabled: u64,
        PacingEnabled: u64,
        MigrationEnabled: u64,
        DatagramReceiveEnabled: u64,
        ServerResumptionLevel: u64,
        MaxOperationsPerDrain: u64,
        MtuDiscoveryMissingProbeCount: u64,
        DestCidUpdateIdleTimeoutMs: u64,
        GreaseQuicBitEnabled: u64,
        EcnEnabled: u64,
        HyStartEnabled: u64,
        StreamRecvWindowBidiLocalDefault: u64,
        StreamRecvWindowBidiRemoteDefault: u64,
        StreamRecvWindowUnidiDefault: u64,
        EncryptionOffloadAllowed: u64,
        ReliableResetEnabled: u64,
        OneWayDelayEnabled: u64,
        NetStatsEventEnabled: u64,
        StreamMultiReceiveEnabled: u64,
        XdpEnabled: u64,
        QTIPEnabled: u64,
        RioEnabled: u64,
        RESERVED: u64,
    ) -> __BindgenBitfieldUnit<[u8; 8usize]> {
        let mut __bindgen_bitfield_unit: __BindgenBitfieldUnit<[u8; 8usize]> = Default::default();
        __bindgen_bitfield_unit.set(0usize, 1u8, {
            let MaxBytesPerKey: u64 = unsafe { ::std::mem::transmute(MaxBytesPerKey) };
            MaxBytesPerKey as u64
        });
        __bindgen_bitfield_unit.set(1usize, 1u8, {
            let HandshakeIdleTimeoutMs: u64 =
                unsafe { ::std::mem::transmute(HandshakeIdleTimeoutMs) };
            HandshakeIdleTimeoutMs as u64
        });
        __bindgen_bitfield_unit.set(2usize, 1u8, {
            let IdleTimeoutMs: u64 = unsafe { ::std::mem::transmute(IdleTimeoutMs) };
            IdleTimeoutMs as u64
        });
        __bindgen_bitfield_unit.set(3usize, 1u8, {
            let MtuDiscoverySearchCompleteTimeoutUs: u64 =
                unsafe { ::std::mem::transmute(MtuDiscoverySearchCompleteTimeoutUs) };
            MtuDiscoverySearchCompleteTimeoutUs as u64
        });
        __bindgen_bitfield_unit.set(4usize, 1u8, {
            let TlsClientMaxSendBuffer: u64 =
                unsafe { ::std::mem::transmute(TlsClientMaxSendBuffer) };
            TlsClientMaxSendBuffer as u64
        });
        __bindgen_bitfield_unit.set(5usize, 1u8, {
            let TlsServerMaxSendBuffer: u64 =
                unsafe { ::std::mem::transmute(TlsServerMaxSendBuffer) };
            TlsServerMaxSendBuffer as u64
        });
        __bindgen_bitfield_unit.set(6usize, 1u8, {
            let StreamRecvWindowDefault: u64 =
                unsafe { ::std::mem::transmute(StreamRecvWindowDefault) };
            StreamRecvWindowDefault as u64
        });
        __bindgen_bitfield_unit.set(7usize, 1u8, {
            let StreamRecvBufferDefault: u64 =
                unsafe { ::std::mem::transmute(StreamRecvBufferDefault) };
            StreamRecvBufferDefault as u64
        });
        __bindgen_bitfield_unit.set(8usize, 1u8, {
            let ConnFlowControlWindow: u64 =
                unsafe { ::std::mem::transmute(ConnFlowControlWindow) };
            ConnFlowControlWindow as u64
        });
        __bindgen_bitfield_unit.set(9usize, 1u8, {
            let MaxWorkerQueueDelayUs: u64 =
                unsafe { ::std::mem::transmute(MaxWorkerQueueDelayUs) };
            MaxWorkerQueueDelayUs as u64
        });
        __bindgen_bitfield_unit.set(10usize, 1u8, {
            let MaxStatelessOperations: u64 =
                unsafe { ::std::mem::transmute(MaxStatelessOperations) };
            MaxStatelessOperations as u64
        });
        __bindgen_bitfield_unit.set(11usize, 1u8, {
            let InitialWindowPackets: u64 = unsafe { ::std::mem::transmute(InitialWindowPackets) };
            InitialWindowPackets as u64
        });
        __bindgen_bitfield_unit.set(12usize, 1u8, {
            let SendIdleTimeoutMs: u64 = unsafe { ::std::mem::transmute(SendIdleTimeoutMs) };
            SendIdleTimeoutMs as u64
        });
        __bindgen_bitfield_unit.set(13usize, 1u8, {
            let InitialRttMs: u64 = unsafe { ::std::mem::transmute(InitialRttMs) };
            InitialRttMs as u64
        });
        __bindgen_bitfield_unit.set(14usize, 1u8, {
            let MaxAckDelayMs: u64 = unsafe { ::std::mem::transmute(MaxAckDelayMs) };
            MaxAckDelayMs as u64
        });
        __bindgen_bitfield_unit.set(15usize, 1u8, {
            let DisconnectTimeoutMs: u64 = unsafe { ::std::mem::transmute(DisconnectTimeoutMs) };
            DisconnectTimeoutMs as u64
        });
        __bindgen_bitfield_unit.set(16usize, 1u8, {
            let KeepAliveIntervalMs: u64 = unsafe { ::std::mem::transmute(KeepAliveIntervalMs) };
            KeepAliveIntervalMs as u64
        });
        __bindgen_bitfield_unit.set(17usize, 1u8, {
            let CongestionControlAlgorithm: u64 =
                unsafe { ::std::mem::transmute(CongestionControlAlgorithm) };
            CongestionControlAlgorithm as u64
        });
        __bindgen_bitfield_unit.set(18usize, 1u8, {
            let PeerBidiStreamCount: u64 = unsafe { ::std::mem::transmute(PeerBidiStreamCount) };
            PeerBidiStreamCount as u64
        });
        __bindgen_bitfield_unit.set(19usize, 1u8, {
            let PeerUnidiStreamCount: u64 = unsafe { ::std::mem::transmute(PeerUnidiStreamCount) };
            PeerUnidiStreamCount as u64
        });
        __bindgen_bitfield_unit.set(20usize, 1u8, {
            let MaxBindingStatelessOperations: u64 =
                unsafe { ::std::mem::transmute(MaxBindingStatelessOperations) };
            MaxBindingStatelessOperations as u64
        });
        __bindgen_bitfield_unit.set(21usize, 1u8, {
            let StatelessOperationExpirationMs: u64 =
                unsafe { ::std::mem::transmute(StatelessOperationExpirationMs) };
            StatelessOperationExpirationMs as u64
        });
        __bindgen_bitfield_unit.set(22usize, 1u8, {
            let MinimumMtu: u64 = unsafe { ::std::mem::transmute(MinimumMtu) };
            MinimumMtu as u64
        });
        __bindgen_bitfield_unit.set(23usize, 1u8, {
            let MaximumMtu: u64 = unsafe { ::std::mem::transmute(MaximumMtu) };
            MaximumMtu as u64
        });
        __bindgen_bitfield_unit.set(24usize, 1u8, {
            let SendBufferingEnabled: u64 = unsafe { ::std::mem::transmute(SendBufferingEnabled) };
            SendBufferingEnabled as u64
        });
        __bindgen_bitfield_unit.set(25usize, 1u8, {
            let PacingEnabled: u64 = unsafe { ::std::mem::transmute(PacingEnabled) };
            PacingEnabled as u64
        });
        __bindgen_bitfield_unit.set(26usize, 1u8, {
            let MigrationEnabled: u64 = unsafe { ::std::mem::transmute(MigrationEnabled) };
            MigrationEnabled as u64
        });
        __bindgen_bitfield_unit.set(27usize, 1u8, {
            let DatagramReceiveEnabled: u64 =
                unsafe { ::std::mem::transmute(DatagramReceiveEnabled) };
            DatagramReceiveEnabled as u64
        });
        __bindgen_bitfield_unit.set(28usize, 1u8, {
            let ServerResumptionLevel: u64 =
                unsafe { ::std::mem::transmute(ServerResumptionLevel) };
            ServerResumptionLevel as u64
        });
        __bindgen_bitfield_unit.set(29usize, 1u8, {
            let MaxOperationsPerDrain: u64 =
                unsafe { ::std::mem::transmute(MaxOperationsPerDrain) };
            MaxOperationsPerDrain as u64
        });
        __bindgen_bitfield_unit.set(30usize, 1u8, {
            let MtuDiscoveryMissingProbeCount: u64 =
                unsafe { ::std::mem::transmute(MtuDiscoveryMissingProbeCount) };
            MtuDiscoveryMissingProbeCount as u64
        });
        __bindgen_bitfield_unit.set(31usize, 1u8, {
            let DestCidUpdateIdleTimeoutMs: u64 =
                unsafe { ::std::mem::transmute(DestCidUpdateIdleTimeoutMs) };
            DestCidUpdateIdleTimeoutMs as u64
        });
        __bindgen_bitfield_unit.set(32usize, 1u8, {
            let GreaseQuicBitEnabled: u64 = unsafe { ::std::mem::transmute(GreaseQuicBitEnabled) };
            GreaseQuicBitEnabled as u64
        });
        __bindgen_bitfield_unit.set(33usize, 1u8, {
            let EcnEnabled: u64 = unsafe { ::std::mem::transmute(EcnEnabled) };
            EcnEnabled as u64
        });
        __bindgen_bitfield_unit.set(34usize, 1u8, {
            let HyStartEnabled: u64 = unsafe { ::std::mem::transmute(HyStartEnabled) };
            HyStartEnabled as u64
        });
        __bindgen_bitfield_unit.set(35usize, 1u8, {
            let StreamRecvWindowBidiLocalDefault: u64 =
                unsafe { ::std::mem::transmute(StreamRecvWindowBidiLocalDefault) };
            StreamRecvWindowBidiLocalDefault as u64
        });
        __bindgen_bitfield_unit.set(36usize, 1u8, {
            let StreamRecvWindowBidiRemoteDefault: u64 =
                unsafe { ::std::mem::transmute(StreamRecvWindowBidiRemoteDefault) };
            StreamRecvWindowBidiRemoteDefault as u64
        });
        __bindgen_bitfield_unit.set(37usize, 1u8, {
            let StreamRecvWindowUnidiDefault: u64 =
                unsafe { ::std::mem::transmute(StreamRecvWindowUnidiDefault) };
            StreamRecvWindowUnidiDefault as u64
        });
        __bindgen_bitfield_unit.set(38usize, 1u8, {
            let EncryptionOffloadAllowed: u64 =
                unsafe { ::std::mem::transmute(EncryptionOffloadAllowed) };
            EncryptionOffloadAllowed as u64
        });
        __bindgen_bitfield_unit.set(39usize, 1u8, {
            let ReliableResetEnabled: u64 = unsafe { ::std::mem::transmute(ReliableResetEnabled) };
            ReliableResetEnabled as u64
        });
        __bindgen_bitfield_unit.set(40usize, 1u8, {
            let OneWayDelayEnabled: u64 = unsafe { ::std::mem::transmute(OneWayDelayEnabled) };
            OneWayDelayEnabled as u64
        });
        __bindgen_bitfield_unit.set(41usize, 1u8, {
            let NetStatsEventEnabled: u64 = unsafe { ::std::mem::transmute(NetStatsEventEnabled) };
            NetStatsEventEnabled as u64
        });
        __bindgen_bitfield_unit.set(42usize, 1u8, {
            let StreamMultiReceiveEnabled: u64 =
                unsafe { ::std::mem::transmute(StreamMultiReceiveEnabled) };
            StreamMultiReceiveEnabled as u64
        });
        __bindgen_bitfield_unit.set(43usize, 1u8, {
            let XdpEnabled: u64 = unsafe { ::std::mem::transmute(XdpEnabled) };
            XdpEnabled as u64
        });
        __bindgen_bitfield_unit.set(44usize, 1u8, {
            let QTIPEnabled: u64 = unsafe { ::std::mem::transmute(QTIPEnabled) };
            QTIPEnabled as u64
        });
        __bindgen_bitfield_unit.set(45usize, 1u8, {
            let RioEnabled: u64 = unsafe { ::std::mem::transmute(RioEnabled) };
            RioEnabled as u64
        });
        __bindgen_bitfield_unit.set(46usize, 18u8, {
            let RESERVED: u64 = unsafe { ::std::mem::transmute(RESERVED) };
            RESERVED as u64
        });
        __bindgen_bitfield_unit
    }
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_SETTINGS__bindgen_ty_1"]
        [::std::mem::size_of::<QUIC_SETTINGS__bindgen_ty_1>() - 8usize];
    ["Alignment of QUIC_SETTINGS__bindgen_ty_1"]
        [::std::mem::align_of::<QUIC_SETTINGS__bindgen_ty_1>() - 8usize];
    ["Offset of field: QUIC_SETTINGS__bindgen_ty_1::IsSetFlags"]
        [::std::mem::offset_of!(QUIC_SETTINGS__bindgen_ty_1, IsSetFlags) - 0usize];
    ["Offset of field: QUIC_SETTINGS__bindgen_ty_1::IsSet"]
        [::std::mem::offset_of!(QUIC_SETTINGS__bindgen_ty_1, IsSet) - 0usize];
};
#[repr(C)]
#[derive(Copy, Clone)]
pub union QUIC_SETTINGS__bindgen_ty_2 {
    pub Flags: u64,
    pub __bindgen_anon_1: QUIC_SETTINGS__bindgen_ty_2__bindgen_ty_1,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_SETTINGS__bindgen_ty_2__bindgen_ty_1 {
    pub _bitfield_align_1: [u64; 0],
    pub _bitfield_1: __BindgenBitfieldUnit<[u8; 8usize]>,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_SETTINGS__bindgen_ty_2__bindgen_ty_1"]
        [::std::mem::size_of::<QUIC_SETTINGS__bindgen_ty_2__bindgen_ty_1>() - 8usize];
    ["Alignment of QUIC_SETTINGS__bindgen_ty_2__bindgen_ty_1"]
        [::std::mem::align_of::<QUIC_SETTINGS__bindgen_ty_2__bindgen_ty_1>() - 8usize];
};
impl QUIC_SETTINGS__bindgen_ty_2__bindgen_ty_1 {
    #[inline]
    pub fn HyStartEnabled(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(0usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_HyStartEnabled(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(0usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn HyStartEnabled_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                0usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_HyStartEnabled_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                0usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn EncryptionOffloadAllowed(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(1usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_EncryptionOffloadAllowed(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(1usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn EncryptionOffloadAllowed_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                1usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_EncryptionOffloadAllowed_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                1usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn ReliableResetEnabled(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(2usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_ReliableResetEnabled(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(2usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn ReliableResetEnabled_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                2usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_ReliableResetEnabled_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                2usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn OneWayDelayEnabled(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(3usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_OneWayDelayEnabled(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(3usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn OneWayDelayEnabled_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                3usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_OneWayDelayEnabled_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                3usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn NetStatsEventEnabled(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(4usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_NetStatsEventEnabled(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(4usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn NetStatsEventEnabled_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                4usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_NetStatsEventEnabled_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                4usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn StreamMultiReceiveEnabled(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(5usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_StreamMultiReceiveEnabled(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(5usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn StreamMultiReceiveEnabled_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                5usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_StreamMultiReceiveEnabled_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                5usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn XdpEnabled(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(6usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_XdpEnabled(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(6usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn XdpEnabled_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                6usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_XdpEnabled_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                6usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn QTIPEnabled(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(7usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_QTIPEnabled(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(7usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn QTIPEnabled_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                7usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_QTIPEnabled_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                7usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn RioEnabled(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(8usize, 1u8) as u64) }
    }
    #[inline]
    pub fn set_RioEnabled(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(8usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn RioEnabled_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                8usize,
                1u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_RioEnabled_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                8usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn ReservedFlags(&self) -> u64 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(9usize, 55u8) as u64) }
    }
    #[inline]
    pub fn set_ReservedFlags(&mut self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            self._bitfield_1.set(9usize, 55u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn ReservedFlags_raw(this: *const Self) -> u64 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 8usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                9usize,
                55u8,
            ) as u64)
        }
    }
    #[inline]
    pub unsafe fn set_ReservedFlags_raw(this: *mut Self, val: u64) {
        unsafe {
            let val: u64 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 8usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                9usize,
                55u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn new_bitfield_1(
        HyStartEnabled: u64,
        EncryptionOffloadAllowed: u64,
        ReliableResetEnabled: u64,
        OneWayDelayEnabled: u64,
        NetStatsEventEnabled: u64,
        StreamMultiReceiveEnabled: u64,
        XdpEnabled: u64,
        QTIPEnabled: u64,
        RioEnabled: u64,
        ReservedFlags: u64,
    ) -> __BindgenBitfieldUnit<[u8; 8usize]> {
        let mut __bindgen_bitfield_unit: __BindgenBitfieldUnit<[u8; 8usize]> = Default::default();
        __bindgen_bitfield_unit.set(0usize, 1u8, {
            let HyStartEnabled: u64 = unsafe { ::std::mem::transmute(HyStartEnabled) };
            HyStartEnabled as u64
        });
        __bindgen_bitfield_unit.set(1usize, 1u8, {
            let EncryptionOffloadAllowed: u64 =
                unsafe { ::std::mem::transmute(EncryptionOffloadAllowed) };
            EncryptionOffloadAllowed as u64
        });
        __bindgen_bitfield_unit.set(2usize, 1u8, {
            let ReliableResetEnabled: u64 = unsafe { ::std::mem::transmute(ReliableResetEnabled) };
            ReliableResetEnabled as u64
        });
        __bindgen_bitfield_unit.set(3usize, 1u8, {
            let OneWayDelayEnabled: u64 = unsafe { ::std::mem::transmute(OneWayDelayEnabled) };
            OneWayDelayEnabled as u64
        });
        __bindgen_bitfield_unit.set(4usize, 1u8, {
            let NetStatsEventEnabled: u64 = unsafe { ::std::mem::transmute(NetStatsEventEnabled) };
            NetStatsEventEnabled as u64
        });
        __bindgen_bitfield_unit.set(5usize, 1u8, {
            let StreamMultiReceiveEnabled: u64 =
                unsafe { ::std::mem::transmute(StreamMultiReceiveEnabled) };
            StreamMultiReceiveEnabled as u64
        });
        __bindgen_bitfield_unit.set(6usize, 1u8, {
            let XdpEnabled: u64 = unsafe { ::std::mem::transmute(XdpEnabled) };
            XdpEnabled as u64
        });
        __bindgen_bitfield_unit.set(7usize, 1u8, {
            let QTIPEnabled: u64 = unsafe { ::std::mem::transmute(QTIPEnabled) };
            QTIPEnabled as u64
        });
        __bindgen_bitfield_unit.set(8usize, 1u8, {
            let RioEnabled: u64 = unsafe { ::std::mem::transmute(RioEnabled) };
            RioEnabled as u64
        });
        __bindgen_bitfield_unit.set(9usize, 55u8, {
            let ReservedFlags: u64 = unsafe { ::std::mem::transmute(ReservedFlags) };
            ReservedFlags as u64
        });
        __bindgen_bitfield_unit
    }
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_SETTINGS__bindgen_ty_2"]
        [::std::mem::size_of::<QUIC_SETTINGS__bindgen_ty_2>() - 8usize];
    ["Alignment of QUIC_SETTINGS__bindgen_ty_2"]
        [::std::mem::align_of::<QUIC_SETTINGS__bindgen_ty_2>() - 8usize];
    ["Offset of field: QUIC_SETTINGS__bindgen_ty_2::Flags"]
        [::std::mem::offset_of!(QUIC_SETTINGS__bindgen_ty_2, Flags) - 0usize];
};
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_SETTINGS"][::std::mem::size_of::<QUIC_SETTINGS>() - 144usize];
    ["Alignment of QUIC_SETTINGS"][::std::mem::align_of::<QUIC_SETTINGS>() - 8usize];
    ["Offset of field: QUIC_SETTINGS::MaxBytesPerKey"]
        [::std::mem::offset_of!(QUIC_SETTINGS, MaxBytesPerKey) - 8usize];
    ["Offset of field: QUIC_SETTINGS::HandshakeIdleTimeoutMs"]
        [::std::mem::offset_of!(QUIC_SETTINGS, HandshakeIdleTimeoutMs) - 16usize];
    ["Offset of field: QUIC_SETTINGS::IdleTimeoutMs"]
        [::std::mem::offset_of!(QUIC_SETTINGS, IdleTimeoutMs) - 24usize];
    ["Offset of field: QUIC_SETTINGS::MtuDiscoverySearchCompleteTimeoutUs"]
        [::std::mem::offset_of!(QUIC_SETTINGS, MtuDiscoverySearchCompleteTimeoutUs) - 32usize];
    ["Offset of field: QUIC_SETTINGS::TlsClientMaxSendBuffer"]
        [::std::mem::offset_of!(QUIC_SETTINGS, TlsClientMaxSendBuffer) - 40usize];
    ["Offset of field: QUIC_SETTINGS::TlsServerMaxSendBuffer"]
        [::std::mem::offset_of!(QUIC_SETTINGS, TlsServerMaxSendBuffer) - 44usize];
    ["Offset of field: QUIC_SETTINGS::StreamRecvWindowDefault"]
        [::std::mem::offset_of!(QUIC_SETTINGS, StreamRecvWindowDefault) - 48usize];
    ["Offset of field: QUIC_SETTINGS::StreamRecvBufferDefault"]
        [::std::mem::offset_of!(QUIC_SETTINGS, StreamRecvBufferDefault) - 52usize];
    ["Offset of field: QUIC_SETTINGS::ConnFlowControlWindow"]
        [::std::mem::offset_of!(QUIC_SETTINGS, ConnFlowControlWindow) - 56usize];
    ["Offset of field: QUIC_SETTINGS::MaxWorkerQueueDelayUs"]
        [::std::mem::offset_of!(QUIC_SETTINGS, MaxWorkerQueueDelayUs) - 60usize];
    ["Offset of field: QUIC_SETTINGS::MaxStatelessOperations"]
        [::std::mem::offset_of!(QUIC_SETTINGS, MaxStatelessOperations) - 64usize];
    ["Offset of field: QUIC_SETTINGS::InitialWindowPackets"]
        [::std::mem::offset_of!(QUIC_SETTINGS, InitialWindowPackets) - 68usize];
    ["Offset of field: QUIC_SETTINGS::SendIdleTimeoutMs"]
        [::std::mem::offset_of!(QUIC_SETTINGS, SendIdleTimeoutMs) - 72usize];
    ["Offset of field: QUIC_SETTINGS::InitialRttMs"]
        [::std::mem::offset_of!(QUIC_SETTINGS, InitialRttMs) - 76usize];
    ["Offset of field: QUIC_SETTINGS::MaxAckDelayMs"]
        [::std::mem::offset_of!(QUIC_SETTINGS, MaxAckDelayMs) - 80usize];
    ["Offset of field: QUIC_SETTINGS::DisconnectTimeoutMs"]
        [::std::mem::offset_of!(QUIC_SETTINGS, DisconnectTimeoutMs) - 84usize];
    ["Offset of field: QUIC_SETTINGS::KeepAliveIntervalMs"]
        [::std::mem::offset_of!(QUIC_SETTINGS, KeepAliveIntervalMs) - 88usize];
    ["Offset of field: QUIC_SETTINGS::CongestionControlAlgorithm"]
        [::std::mem::offset_of!(QUIC_SETTINGS, CongestionControlAlgorithm) - 92usize];
    ["Offset of field: QUIC_SETTINGS::PeerBidiStreamCount"]
        [::std::mem::offset_of!(QUIC_SETTINGS, PeerBidiStreamCount) - 94usize];
    ["Offset of field: QUIC_SETTINGS::PeerUnidiStreamCount"]
        [::std::mem::offset_of!(QUIC_SETTINGS, PeerUnidiStreamCount) - 96usize];
    ["Offset of field: QUIC_SETTINGS::MaxBindingStatelessOperations"]
        [::std::mem::offset_of!(QUIC_SETTINGS, MaxBindingStatelessOperations) - 98usize];
    ["Offset of field: QUIC_SETTINGS::StatelessOperationExpirationMs"]
        [::std::mem::offset_of!(QUIC_SETTINGS, StatelessOperationExpirationMs) - 100usize];
    ["Offset of field: QUIC_SETTINGS::MinimumMtu"]
        [::std::mem::offset_of!(QUIC_SETTINGS, MinimumMtu) - 102usize];
    ["Offset of field: QUIC_SETTINGS::MaximumMtu"]
        [::std::mem::offset_of!(QUIC_SETTINGS, MaximumMtu) - 104usize];
    ["Offset of field: QUIC_SETTINGS::MaxOperationsPerDrain"]
        [::std::mem::offset_of!(QUIC_SETTINGS, MaxOperationsPerDrain) - 107usize];
    ["Offset of field: QUIC_SETTINGS::MtuDiscoveryMissingProbeCount"]
        [::std::mem::offset_of!(QUIC_SETTINGS, MtuDiscoveryMissingProbeCount) - 108usize];
    ["Offset of field: QUIC_SETTINGS::DestCidUpdateIdleTimeoutMs"]
        [::std::mem::offset_of!(QUIC_SETTINGS, DestCidUpdateIdleTimeoutMs) - 112usize];
    ["Offset of field: QUIC_SETTINGS::StreamRecvWindowBidiLocalDefault"]
        [::std::mem::offset_of!(QUIC_SETTINGS, StreamRecvWindowBidiLocalDefault) - 128usize];
    ["Offset of field: QUIC_SETTINGS::StreamRecvWindowBidiRemoteDefault"]
        [::std::mem::offset_of!(QUIC_SETTINGS, StreamRecvWindowBidiRemoteDefault) - 132usize];
    ["Offset of field: QUIC_SETTINGS::StreamRecvWindowUnidiDefault"]
        [::std::mem::offset_of!(QUIC_SETTINGS, StreamRecvWindowUnidiDefault) - 136usize];
};
impl QUIC_SETTINGS {
    #[inline]
    pub fn SendBufferingEnabled(&self) -> u8 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(0usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_SendBufferingEnabled(&mut self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(0usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn SendBufferingEnabled_raw(this: *const Self) -> u8 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                0usize,
                1u8,
            ) as u8)
        }
    }
    #[inline]
    pub unsafe fn set_SendBufferingEnabled_raw(this: *mut Self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                0usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn PacingEnabled(&self) -> u8 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(1usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_PacingEnabled(&mut self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(1usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn PacingEnabled_raw(this: *const Self) -> u8 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                1usize,
                1u8,
            ) as u8)
        }
    }
    #[inline]
    pub unsafe fn set_PacingEnabled_raw(this: *mut Self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                1usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn MigrationEnabled(&self) -> u8 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(2usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_MigrationEnabled(&mut self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(2usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn MigrationEnabled_raw(this: *const Self) -> u8 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                2usize,
                1u8,
            ) as u8)
        }
    }
    #[inline]
    pub unsafe fn set_MigrationEnabled_raw(this: *mut Self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                2usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn DatagramReceiveEnabled(&self) -> u8 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(3usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_DatagramReceiveEnabled(&mut self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(3usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn DatagramReceiveEnabled_raw(this: *const Self) -> u8 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                3usize,
                1u8,
            ) as u8)
        }
    }
    #[inline]
    pub unsafe fn set_DatagramReceiveEnabled_raw(this: *mut Self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                3usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn ServerResumptionLevel(&self) -> u8 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(4usize, 2u8) as u8) }
    }
    #[inline]
    pub fn set_ServerResumptionLevel(&mut self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(4usize, 2u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn ServerResumptionLevel_raw(this: *const Self) -> u8 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                4usize,
                2u8,
            ) as u8)
        }
    }
    #[inline]
    pub unsafe fn set_ServerResumptionLevel_raw(this: *mut Self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                4usize,
                2u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn GreaseQuicBitEnabled(&self) -> u8 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(6usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_GreaseQuicBitEnabled(&mut self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(6usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn GreaseQuicBitEnabled_raw(this: *const Self) -> u8 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                6usize,
                1u8,
            ) as u8)
        }
    }
    #[inline]
    pub unsafe fn set_GreaseQuicBitEnabled_raw(this: *mut Self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                6usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn EcnEnabled(&self) -> u8 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(7usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_EcnEnabled(&mut self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(7usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn EcnEnabled_raw(this: *const Self) -> u8 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                7usize,
                1u8,
            ) as u8)
        }
    }
    #[inline]
    pub unsafe fn set_EcnEnabled_raw(this: *mut Self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                7usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn new_bitfield_1(
        SendBufferingEnabled: u8,
        PacingEnabled: u8,
        MigrationEnabled: u8,
        DatagramReceiveEnabled: u8,
        ServerResumptionLevel: u8,
        GreaseQuicBitEnabled: u8,
        EcnEnabled: u8,
    ) -> __BindgenBitfieldUnit<[u8; 1usize]> {
        let mut __bindgen_bitfield_unit: __BindgenBitfieldUnit<[u8; 1usize]> = Default::default();
        __bindgen_bitfield_unit.set(0usize, 1u8, {
            let SendBufferingEnabled: u8 = unsafe { ::std::mem::transmute(SendBufferingEnabled) };
            SendBufferingEnabled as u64
        });
        __bindgen_bitfield_unit.set(1usize, 1u8, {
            let PacingEnabled: u8 = unsafe { ::std::mem::transmute(PacingEnabled) };
            PacingEnabled as u64
        });
        __bindgen_bitfield_unit.set(2usize, 1u8, {
            let MigrationEnabled: u8 = unsafe { ::std::mem::transmute(MigrationEnabled) };
            MigrationEnabled as u64
        });
        __bindgen_bitfield_unit.set(3usize, 1u8, {
            let DatagramReceiveEnabled: u8 =
                unsafe { ::std::mem::transmute(DatagramReceiveEnabled) };
            DatagramReceiveEnabled as u64
        });
        __bindgen_bitfield_unit.set(4usize, 2u8, {
            let ServerResumptionLevel: u8 = unsafe { ::std::mem::transmute(ServerResumptionLevel) };
            ServerResumptionLevel as u64
        });
        __bindgen_bitfield_unit.set(6usize, 1u8, {
            let GreaseQuicBitEnabled: u8 = unsafe { ::std::mem::transmute(GreaseQuicBitEnabled) };
            GreaseQuicBitEnabled as u64
        });
        __bindgen_bitfield_unit.set(7usize, 1u8, {
            let EcnEnabled: u8 = unsafe { ::std::mem::transmute(EcnEnabled) };
            EcnEnabled as u64
        });
        __bindgen_bitfield_unit
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_TLS_SECRETS {
    pub SecretLength: u8,
    pub IsSet: QUIC_TLS_SECRETS__bindgen_ty_1,
    pub ClientRandom: [u8; 32usize],
    pub ClientEarlyTrafficSecret: [u8; 64usize],
    pub ClientHandshakeTrafficSecret: [u8; 64usize],
    pub ServerHandshakeTrafficSecret: [u8; 64usize],
    pub ClientTrafficSecret0: [u8; 64usize],
    pub ServerTrafficSecret0: [u8; 64usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_TLS_SECRETS__bindgen_ty_1 {
    pub _bitfield_align_1: [u8; 0],
    pub _bitfield_1: __BindgenBitfieldUnit<[u8; 1usize]>,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_TLS_SECRETS__bindgen_ty_1"]
        [::std::mem::size_of::<QUIC_TLS_SECRETS__bindgen_ty_1>() - 1usize];
    ["Alignment of QUIC_TLS_SECRETS__bindgen_ty_1"]
        [::std::mem::align_of::<QUIC_TLS_SECRETS__bindgen_ty_1>() - 1usize];
};
impl QUIC_TLS_SECRETS__bindgen_ty_1 {
    #[inline]
    pub fn ClientRandom(&self) -> u8 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(0usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_ClientRandom(&mut self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(0usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn ClientRandom_raw(this: *const Self) -> u8 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                0usize,
                1u8,
            ) as u8)
        }
    }
    #[inline]
    pub unsafe fn set_ClientRandom_raw(this: *mut Self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                0usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn ClientEarlyTrafficSecret(&self) -> u8 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(1usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_ClientEarlyTrafficSecret(&mut self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(1usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn ClientEarlyTrafficSecret_raw(this: *const Self) -> u8 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                1usize,
                1u8,
            ) as u8)
        }
    }
    #[inline]
    pub unsafe fn set_ClientEarlyTrafficSecret_raw(this: *mut Self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                1usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn ClientHandshakeTrafficSecret(&self) -> u8 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(2usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_ClientHandshakeTrafficSecret(&mut self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(2usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn ClientHandshakeTrafficSecret_raw(this: *const Self) -> u8 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                2usize,
                1u8,
            ) as u8)
        }
    }
    #[inline]
    pub unsafe fn set_ClientHandshakeTrafficSecret_raw(this: *mut Self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                2usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn ServerHandshakeTrafficSecret(&self) -> u8 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(3usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_ServerHandshakeTrafficSecret(&mut self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(3usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn ServerHandshakeTrafficSecret_raw(this: *const Self) -> u8 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                3usize,
                1u8,
            ) as u8)
        }
    }
    #[inline]
    pub unsafe fn set_ServerHandshakeTrafficSecret_raw(this: *mut Self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                3usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn ClientTrafficSecret0(&self) -> u8 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(4usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_ClientTrafficSecret0(&mut self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(4usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn ClientTrafficSecret0_raw(this: *const Self) -> u8 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                4usize,
                1u8,
            ) as u8)
        }
    }
    #[inline]
    pub unsafe fn set_ClientTrafficSecret0_raw(this: *mut Self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                4usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn ServerTrafficSecret0(&self) -> u8 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(5usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_ServerTrafficSecret0(&mut self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(5usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn ServerTrafficSecret0_raw(this: *const Self) -> u8 {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                5usize,
                1u8,
            ) as u8)
        }
    }
    #[inline]
    pub unsafe fn set_ServerTrafficSecret0_raw(this: *mut Self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                5usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn new_bitfield_1(
        ClientRandom: u8,
        ClientEarlyTrafficSecret: u8,
        ClientHandshakeTrafficSecret: u8,
        ServerHandshakeTrafficSecret: u8,
        ClientTrafficSecret0: u8,
        ServerTrafficSecret0: u8,
    ) -> __BindgenBitfieldUnit<[u8; 1usize]> {
        let mut __bindgen_bitfield_unit: __BindgenBitfieldUnit<[u8; 1usize]> = Default::default();
        __bindgen_bitfield_unit.set(0usize, 1u8, {
            let ClientRandom: u8 = unsafe { ::std::mem::transmute(ClientRandom) };
            ClientRandom as u64
        });
        __bindgen_bitfield_unit.set(1usize, 1u8, {
            let ClientEarlyTrafficSecret: u8 =
                unsafe { ::std::mem::transmute(ClientEarlyTrafficSecret) };
            ClientEarlyTrafficSecret as u64
        });
        __bindgen_bitfield_unit.set(2usize, 1u8, {
            let ClientHandshakeTrafficSecret: u8 =
                unsafe { ::std::mem::transmute(ClientHandshakeTrafficSecret) };
            ClientHandshakeTrafficSecret as u64
        });
        __bindgen_bitfield_unit.set(3usize, 1u8, {
            let ServerHandshakeTrafficSecret: u8 =
                unsafe { ::std::mem::transmute(ServerHandshakeTrafficSecret) };
            ServerHandshakeTrafficSecret as u64
        });
        __bindgen_bitfield_unit.set(4usize, 1u8, {
            let ClientTrafficSecret0: u8 = unsafe { ::std::mem::transmute(ClientTrafficSecret0) };
            ClientTrafficSecret0 as u64
        });
        __bindgen_bitfield_unit.set(5usize, 1u8, {
            let ServerTrafficSecret0: u8 = unsafe { ::std::mem::transmute(ServerTrafficSecret0) };
            ServerTrafficSecret0 as u64
        });
        __bindgen_bitfield_unit
    }
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_TLS_SECRETS"][::std::mem::size_of::<QUIC_TLS_SECRETS>() - 354usize];
    ["Alignment of QUIC_TLS_SECRETS"][::std::mem::align_of::<QUIC_TLS_SECRETS>() - 1usize];
    ["Offset of field: QUIC_TLS_SECRETS::SecretLength"]
        [::std::mem::offset_of!(QUIC_TLS_SECRETS, SecretLength) - 0usize];
    ["Offset of field: QUIC_TLS_SECRETS::IsSet"]
        [::std::mem::offset_of!(QUIC_TLS_SECRETS, IsSet) - 1usize];
    ["Offset of field: QUIC_TLS_SECRETS::ClientRandom"]
        [::std::mem::offset_of!(QUIC_TLS_SECRETS, ClientRandom) - 2usize];
    ["Offset of field: QUIC_TLS_SECRETS::ClientEarlyTrafficSecret"]
        [::std::mem::offset_of!(QUIC_TLS_SECRETS, ClientEarlyTrafficSecret) - 34usize];
    ["Offset of field: QUIC_TLS_SECRETS::ClientHandshakeTrafficSecret"]
        [::std::mem::offset_of!(QUIC_TLS_SECRETS, ClientHandshakeTrafficSecret) - 98usize];
    ["Offset of field: QUIC_TLS_SECRETS::ServerHandshakeTrafficSecret"]
        [::std::mem::offset_of!(QUIC_TLS_SECRETS, ServerHandshakeTrafficSecret) - 162usize];
    ["Offset of field: QUIC_TLS_SECRETS::ClientTrafficSecret0"]
        [::std::mem::offset_of!(QUIC_TLS_SECRETS, ClientTrafficSecret0) - 226usize];
    ["Offset of field: QUIC_TLS_SECRETS::ServerTrafficSecret0"]
        [::std::mem::offset_of!(QUIC_TLS_SECRETS, ServerTrafficSecret0) - 290usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_STREAM_STATISTICS {
    pub ConnBlockedBySchedulingUs: u64,
    pub ConnBlockedByPacingUs: u64,
    pub ConnBlockedByAmplificationProtUs: u64,
    pub ConnBlockedByCongestionControlUs: u64,
    pub ConnBlockedByFlowControlUs: u64,
    pub StreamBlockedByIdFlowControlUs: u64,
    pub StreamBlockedByFlowControlUs: u64,
    pub StreamBlockedByAppUs: u64,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_STREAM_STATISTICS"][::std::mem::size_of::<QUIC_STREAM_STATISTICS>() - 64usize];
    ["Alignment of QUIC_STREAM_STATISTICS"]
        [::std::mem::align_of::<QUIC_STREAM_STATISTICS>() - 8usize];
    ["Offset of field: QUIC_STREAM_STATISTICS::ConnBlockedBySchedulingUs"]
        [::std::mem::offset_of!(QUIC_STREAM_STATISTICS, ConnBlockedBySchedulingUs) - 0usize];
    ["Offset of field: QUIC_STREAM_STATISTICS::ConnBlockedByPacingUs"]
        [::std::mem::offset_of!(QUIC_STREAM_STATISTICS, ConnBlockedByPacingUs) - 8usize];
    ["Offset of field: QUIC_STREAM_STATISTICS::ConnBlockedByAmplificationProtUs"][::std::mem::offset_of!(
        QUIC_STREAM_STATISTICS,
        ConnBlockedByAmplificationProtUs
    ) - 16usize];
    ["Offset of field: QUIC_STREAM_STATISTICS::ConnBlockedByCongestionControlUs"][::std::mem::offset_of!(
        QUIC_STREAM_STATISTICS,
        ConnBlockedByCongestionControlUs
    ) - 24usize];
    ["Offset of field: QUIC_STREAM_STATISTICS::ConnBlockedByFlowControlUs"]
        [::std::mem::offset_of!(QUIC_STREAM_STATISTICS, ConnBlockedByFlowControlUs) - 32usize];
    ["Offset of field: QUIC_STREAM_STATISTICS::StreamBlockedByIdFlowControlUs"]
        [::std::mem::offset_of!(QUIC_STREAM_STATISTICS, StreamBlockedByIdFlowControlUs) - 40usize];
    ["Offset of field: QUIC_STREAM_STATISTICS::StreamBlockedByFlowControlUs"]
        [::std::mem::offset_of!(QUIC_STREAM_STATISTICS, StreamBlockedByFlowControlUs) - 48usize];
    ["Offset of field: QUIC_STREAM_STATISTICS::StreamBlockedByAppUs"]
        [::std::mem::offset_of!(QUIC_STREAM_STATISTICS, StreamBlockedByAppUs) - 56usize];
};
pub type QUIC_SET_CONTEXT_FN = ::std::option::Option<
    unsafe extern "C" fn(Handle: HQUIC, Context: *mut ::std::os::raw::c_void),
>;
pub type QUIC_GET_CONTEXT_FN =
    ::std::option::Option<unsafe extern "C" fn(Handle: HQUIC) -> *mut ::std::os::raw::c_void>;
pub type QUIC_SET_CALLBACK_HANDLER_FN = ::std::option::Option<
    unsafe extern "C" fn(
        Handle: HQUIC,
        Handler: *mut ::std::os::raw::c_void,
        Context: *mut ::std::os::raw::c_void,
    ),
>;
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_SCHANNEL_CREDENTIAL_ATTRIBUTE_W {
    pub Attribute: ::std::os::raw::c_ulong,
    pub BufferLength: ::std::os::raw::c_ulong,
    pub Buffer: *mut ::std::os::raw::c_void,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_SCHANNEL_CREDENTIAL_ATTRIBUTE_W"]
        [::std::mem::size_of::<QUIC_SCHANNEL_CREDENTIAL_ATTRIBUTE_W>() - 16usize];
    ["Alignment of QUIC_SCHANNEL_CREDENTIAL_ATTRIBUTE_W"]
        [::std::mem::align_of::<QUIC_SCHANNEL_CREDENTIAL_ATTRIBUTE_W>() - 8usize];
    ["Offset of field: QUIC_SCHANNEL_CREDENTIAL_ATTRIBUTE_W::Attribute"]
        [::std::mem::offset_of!(QUIC_SCHANNEL_CREDENTIAL_ATTRIBUTE_W, Attribute) - 0usize];
    ["Offset of field: QUIC_SCHANNEL_CREDENTIAL_ATTRIBUTE_W::BufferLength"]
        [::std::mem::offset_of!(QUIC_SCHANNEL_CREDENTIAL_ATTRIBUTE_W, BufferLength) - 4usize];
    ["Offset of field: QUIC_SCHANNEL_CREDENTIAL_ATTRIBUTE_W::Buffer"]
        [::std::mem::offset_of!(QUIC_SCHANNEL_CREDENTIAL_ATTRIBUTE_W, Buffer) - 8usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_SCHANNEL_CONTEXT_ATTRIBUTE_W {
    pub Attribute: ::std::os::raw::c_ulong,
    pub Buffer: *mut ::std::os::raw::c_void,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_SCHANNEL_CONTEXT_ATTRIBUTE_W"]
        [::std::mem::size_of::<QUIC_SCHANNEL_CONTEXT_ATTRIBUTE_W>() - 16usize];
    ["Alignment of QUIC_SCHANNEL_CONTEXT_ATTRIBUTE_W"]
        [::std::mem::align_of::<QUIC_SCHANNEL_CONTEXT_ATTRIBUTE_W>() - 8usize];
    ["Offset of field: QUIC_SCHANNEL_CONTEXT_ATTRIBUTE_W::Attribute"]
        [::std::mem::offset_of!(QUIC_SCHANNEL_CONTEXT_ATTRIBUTE_W, Attribute) - 0usize];
    ["Offset of field: QUIC_SCHANNEL_CONTEXT_ATTRIBUTE_W::Buffer"]
        [::std::mem::offset_of!(QUIC_SCHANNEL_CONTEXT_ATTRIBUTE_W, Buffer) - 8usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_SCHANNEL_CONTEXT_ATTRIBUTE_EX_W {
    pub Attribute: ::std::os::raw::c_ulong,
    pub BufferLength: ::std::os::raw::c_ulong,
    pub Buffer: *mut ::std::os::raw::c_void,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_SCHANNEL_CONTEXT_ATTRIBUTE_EX_W"]
        [::std::mem::size_of::<QUIC_SCHANNEL_CONTEXT_ATTRIBUTE_EX_W>() - 16usize];
    ["Alignment of QUIC_SCHANNEL_CONTEXT_ATTRIBUTE_EX_W"]
        [::std::mem::align_of::<QUIC_SCHANNEL_CONTEXT_ATTRIBUTE_EX_W>() - 8usize];
    ["Offset of field: QUIC_SCHANNEL_CONTEXT_ATTRIBUTE_EX_W::Attribute"]
        [::std::mem::offset_of!(QUIC_SCHANNEL_CONTEXT_ATTRIBUTE_EX_W, Attribute) - 0usize];
    ["Offset of field: QUIC_SCHANNEL_CONTEXT_ATTRIBUTE_EX_W::BufferLength"]
        [::std::mem::offset_of!(QUIC_SCHANNEL_CONTEXT_ATTRIBUTE_EX_W, BufferLength) - 4usize];
    ["Offset of field: QUIC_SCHANNEL_CONTEXT_ATTRIBUTE_EX_W::Buffer"]
        [::std::mem::offset_of!(QUIC_SCHANNEL_CONTEXT_ATTRIBUTE_EX_W, Buffer) - 8usize];
};
pub type QUIC_SET_PARAM_FN = ::std::option::Option<
    unsafe extern "C" fn(
        Handle: HQUIC,
        Param: u32,
        BufferLength: u32,
        Buffer: *const ::std::os::raw::c_void,
    ) -> HRESULT,
>;
pub type QUIC_GET_PARAM_FN = ::std::option::Option<
    unsafe extern "C" fn(
        Handle: HQUIC,
        Param: u32,
        BufferLength: *mut u32,
        Buffer: *mut ::std::os::raw::c_void,
    ) -> HRESULT,
>;
pub type QUIC_REGISTRATION_OPEN_FN = ::std::option::Option<
    unsafe extern "C" fn(
        Config: *const QUIC_REGISTRATION_CONFIG,
        Registration: *mut HQUIC,
    ) -> HRESULT,
>;
pub type QUIC_REGISTRATION_CLOSE_FN =
    ::std::option::Option<unsafe extern "C" fn(Registration: HQUIC)>;
pub type QUIC_REGISTRATION_SHUTDOWN_FN = ::std::option::Option<
    unsafe extern "C" fn(
        Registration: HQUIC,
        Flags: QUIC_CONNECTION_SHUTDOWN_FLAGS,
        ErrorCode: QUIC_UINT62,
    ),
>;
pub type QUIC_CONFIGURATION_OPEN_FN = ::std::option::Option<
    unsafe extern "C" fn(
        Registration: HQUIC,
        AlpnBuffers: *const QUIC_BUFFER,
        AlpnBufferCount: u32,
        Settings: *const QUIC_SETTINGS,
        SettingsSize: u32,
        Context: *mut ::std::os::raw::c_void,
        Configuration: *mut HQUIC,
    ) -> HRESULT,
>;
pub type QUIC_CONFIGURATION_CLOSE_FN =
    ::std::option::Option<unsafe extern "C" fn(Configuration: HQUIC)>;
pub type QUIC_CONFIGURATION_LOAD_CREDENTIAL_FN = ::std::option::Option<
    unsafe extern "C" fn(
        Configuration: HQUIC,
        CredConfig: *const QUIC_CREDENTIAL_CONFIG,
    ) -> HRESULT,
>;
pub const QUIC_LISTENER_EVENT_TYPE_QUIC_LISTENER_EVENT_NEW_CONNECTION: QUIC_LISTENER_EVENT_TYPE = 0;
pub const QUIC_LISTENER_EVENT_TYPE_QUIC_LISTENER_EVENT_STOP_COMPLETE: QUIC_LISTENER_EVENT_TYPE = 1;
pub const QUIC_LISTENER_EVENT_TYPE_QUIC_LISTENER_EVENT_DOS_MODE_CHANGED: QUIC_LISTENER_EVENT_TYPE =
    2;
pub type QUIC_LISTENER_EVENT_TYPE = ::std::os::raw::c_int;
#[repr(C)]
#[derive(Copy, Clone)]
pub struct QUIC_LISTENER_EVENT {
    pub Type: QUIC_LISTENER_EVENT_TYPE,
    pub __bindgen_anon_1: QUIC_LISTENER_EVENT__bindgen_ty_1,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union QUIC_LISTENER_EVENT__bindgen_ty_1 {
    pub NEW_CONNECTION: QUIC_LISTENER_EVENT__bindgen_ty_1__bindgen_ty_1,
    pub STOP_COMPLETE: QUIC_LISTENER_EVENT__bindgen_ty_1__bindgen_ty_2,
    pub DOS_MODE_CHANGED: QUIC_LISTENER_EVENT__bindgen_ty_1__bindgen_ty_3,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_LISTENER_EVENT__bindgen_ty_1__bindgen_ty_1 {
    pub Info: *const QUIC_NEW_CONNECTION_INFO,
    pub Connection: HQUIC,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_LISTENER_EVENT__bindgen_ty_1__bindgen_ty_1"]
        [::std::mem::size_of::<QUIC_LISTENER_EVENT__bindgen_ty_1__bindgen_ty_1>() - 16usize];
    ["Alignment of QUIC_LISTENER_EVENT__bindgen_ty_1__bindgen_ty_1"]
        [::std::mem::align_of::<QUIC_LISTENER_EVENT__bindgen_ty_1__bindgen_ty_1>() - 8usize];
    ["Offset of field: QUIC_LISTENER_EVENT__bindgen_ty_1__bindgen_ty_1::Info"]
        [::std::mem::offset_of!(QUIC_LISTENER_EVENT__bindgen_ty_1__bindgen_ty_1, Info) - 0usize];
    ["Offset of field: QUIC_LISTENER_EVENT__bindgen_ty_1__bindgen_ty_1::Connection"][::std::mem::offset_of!(
        QUIC_LISTENER_EVENT__bindgen_ty_1__bindgen_ty_1,
        Connection
    ) - 8usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_LISTENER_EVENT__bindgen_ty_1__bindgen_ty_2 {
    pub _bitfield_align_1: [u8; 0],
    pub _bitfield_1: __BindgenBitfieldUnit<[u8; 1usize]>,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_LISTENER_EVENT__bindgen_ty_1__bindgen_ty_2"]
        [::std::mem::size_of::<QUIC_LISTENER_EVENT__bindgen_ty_1__bindgen_ty_2>() - 1usize];
    ["Alignment of QUIC_LISTENER_EVENT__bindgen_ty_1__bindgen_ty_2"]
        [::std::mem::align_of::<QUIC_LISTENER_EVENT__bindgen_ty_1__bindgen_ty_2>() - 1usize];
};
impl QUIC_LISTENER_EVENT__bindgen_ty_1__bindgen_ty_2 {
    #[inline]
    pub fn AppCloseInProgress(&self) -> BOOLEAN {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(0usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_AppCloseInProgress(&mut self, val: BOOLEAN) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(0usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn AppCloseInProgress_raw(this: *const Self) -> BOOLEAN {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                0usize,
                1u8,
            ) as u8)
        }
    }
    #[inline]
    pub unsafe fn set_AppCloseInProgress_raw(this: *mut Self, val: BOOLEAN) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                0usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn RESERVED(&self) -> BOOLEAN {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(1usize, 7u8) as u8) }
    }
    #[inline]
    pub fn set_RESERVED(&mut self, val: BOOLEAN) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(1usize, 7u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn RESERVED_raw(this: *const Self) -> BOOLEAN {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                1usize,
                7u8,
            ) as u8)
        }
    }
    #[inline]
    pub unsafe fn set_RESERVED_raw(this: *mut Self, val: BOOLEAN) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                1usize,
                7u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn new_bitfield_1(
        AppCloseInProgress: BOOLEAN,
        RESERVED: BOOLEAN,
    ) -> __BindgenBitfieldUnit<[u8; 1usize]> {
        let mut __bindgen_bitfield_unit: __BindgenBitfieldUnit<[u8; 1usize]> = Default::default();
        __bindgen_bitfield_unit.set(0usize, 1u8, {
            let AppCloseInProgress: u8 = unsafe { ::std::mem::transmute(AppCloseInProgress) };
            AppCloseInProgress as u64
        });
        __bindgen_bitfield_unit.set(1usize, 7u8, {
            let RESERVED: u8 = unsafe { ::std::mem::transmute(RESERVED) };
            RESERVED as u64
        });
        __bindgen_bitfield_unit
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_LISTENER_EVENT__bindgen_ty_1__bindgen_ty_3 {
    pub _bitfield_align_1: [u8; 0],
    pub _bitfield_1: __BindgenBitfieldUnit<[u8; 1usize]>,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_LISTENER_EVENT__bindgen_ty_1__bindgen_ty_3"]
        [::std::mem::size_of::<QUIC_LISTENER_EVENT__bindgen_ty_1__bindgen_ty_3>() - 1usize];
    ["Alignment of QUIC_LISTENER_EVENT__bindgen_ty_1__bindgen_ty_3"]
        [::std::mem::align_of::<QUIC_LISTENER_EVENT__bindgen_ty_1__bindgen_ty_3>() - 1usize];
};
impl QUIC_LISTENER_EVENT__bindgen_ty_1__bindgen_ty_3 {
    #[inline]
    pub fn DosModeEnabled(&self) -> BOOLEAN {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(0usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_DosModeEnabled(&mut self, val: BOOLEAN) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(0usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn DosModeEnabled_raw(this: *const Self) -> BOOLEAN {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                0usize,
                1u8,
            ) as u8)
        }
    }
    #[inline]
    pub unsafe fn set_DosModeEnabled_raw(this: *mut Self, val: BOOLEAN) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                0usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn RESERVED(&self) -> BOOLEAN {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(1usize, 7u8) as u8) }
    }
    #[inline]
    pub fn set_RESERVED(&mut self, val: BOOLEAN) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(1usize, 7u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn RESERVED_raw(this: *const Self) -> BOOLEAN {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                1usize,
                7u8,
            ) as u8)
        }
    }
    #[inline]
    pub unsafe fn set_RESERVED_raw(this: *mut Self, val: BOOLEAN) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                1usize,
                7u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn new_bitfield_1(
        DosModeEnabled: BOOLEAN,
        RESERVED: BOOLEAN,
    ) -> __BindgenBitfieldUnit<[u8; 1usize]> {
        let mut __bindgen_bitfield_unit: __BindgenBitfieldUnit<[u8; 1usize]> = Default::default();
        __bindgen_bitfield_unit.set(0usize, 1u8, {
            let DosModeEnabled: u8 = unsafe { ::std::mem::transmute(DosModeEnabled) };
            DosModeEnabled as u64
        });
        __bindgen_bitfield_unit.set(1usize, 7u8, {
            let RESERVED: u8 = unsafe { ::std::mem::transmute(RESERVED) };
            RESERVED as u64
        });
        __bindgen_bitfield_unit
    }
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_LISTENER_EVENT__bindgen_ty_1"]
        [::std::mem::size_of::<QUIC_LISTENER_EVENT__bindgen_ty_1>() - 16usize];
    ["Alignment of QUIC_LISTENER_EVENT__bindgen_ty_1"]
        [::std::mem::align_of::<QUIC_LISTENER_EVENT__bindgen_ty_1>() - 8usize];
    ["Offset of field: QUIC_LISTENER_EVENT__bindgen_ty_1::NEW_CONNECTION"]
        [::std::mem::offset_of!(QUIC_LISTENER_EVENT__bindgen_ty_1, NEW_CONNECTION) - 0usize];
    ["Offset of field: QUIC_LISTENER_EVENT__bindgen_ty_1::STOP_COMPLETE"]
        [::std::mem::offset_of!(QUIC_LISTENER_EVENT__bindgen_ty_1, STOP_COMPLETE) - 0usize];
    ["Offset of field: QUIC_LISTENER_EVENT__bindgen_ty_1::DOS_MODE_CHANGED"]
        [::std::mem::offset_of!(QUIC_LISTENER_EVENT__bindgen_ty_1, DOS_MODE_CHANGED) - 0usize];
};
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_LISTENER_EVENT"][::std::mem::size_of::<QUIC_LISTENER_EVENT>() - 24usize];
    ["Alignment of QUIC_LISTENER_EVENT"][::std::mem::align_of::<QUIC_LISTENER_EVENT>() - 8usize];
    ["Offset of field: QUIC_LISTENER_EVENT::Type"]
        [::std::mem::offset_of!(QUIC_LISTENER_EVENT, Type) - 0usize];
};
pub type QUIC_LISTENER_CALLBACK_HANDLER = ::std::option::Option<
    unsafe extern "C" fn(
        arg1: HQUIC,
        arg2: *mut ::std::os::raw::c_void,
        arg3: *mut QUIC_LISTENER_EVENT,
    ) -> HRESULT,
>;
pub type QUIC_LISTENER_OPEN_FN = ::std::option::Option<
    unsafe extern "C" fn(
        Registration: HQUIC,
        Handler: QUIC_LISTENER_CALLBACK_HANDLER,
        Context: *mut ::std::os::raw::c_void,
        Listener: *mut HQUIC,
    ) -> HRESULT,
>;
pub type QUIC_LISTENER_CLOSE_FN = ::std::option::Option<unsafe extern "C" fn(Listener: HQUIC)>;
pub type QUIC_LISTENER_START_FN = ::std::option::Option<
    unsafe extern "C" fn(
        Listener: HQUIC,
        AlpnBuffers: *const QUIC_BUFFER,
        AlpnBufferCount: u32,
        LocalAddress: *const QUIC_ADDR,
    ) -> HRESULT,
>;
pub type QUIC_LISTENER_STOP_FN = ::std::option::Option<unsafe extern "C" fn(Listener: HQUIC)>;
pub const QUIC_CONNECTION_EVENT_TYPE_QUIC_CONNECTION_EVENT_CONNECTED: QUIC_CONNECTION_EVENT_TYPE =
    0;
pub const QUIC_CONNECTION_EVENT_TYPE_QUIC_CONNECTION_EVENT_SHUTDOWN_INITIATED_BY_TRANSPORT:
    QUIC_CONNECTION_EVENT_TYPE = 1;
pub const QUIC_CONNECTION_EVENT_TYPE_QUIC_CONNECTION_EVENT_SHUTDOWN_INITIATED_BY_PEER:
    QUIC_CONNECTION_EVENT_TYPE = 2;
pub const QUIC_CONNECTION_EVENT_TYPE_QUIC_CONNECTION_EVENT_SHUTDOWN_COMPLETE:
    QUIC_CONNECTION_EVENT_TYPE = 3;
pub const QUIC_CONNECTION_EVENT_TYPE_QUIC_CONNECTION_EVENT_LOCAL_ADDRESS_CHANGED:
    QUIC_CONNECTION_EVENT_TYPE = 4;
pub const QUIC_CONNECTION_EVENT_TYPE_QUIC_CONNECTION_EVENT_PEER_ADDRESS_CHANGED:
    QUIC_CONNECTION_EVENT_TYPE = 5;
pub const QUIC_CONNECTION_EVENT_TYPE_QUIC_CONNECTION_EVENT_PEER_STREAM_STARTED:
    QUIC_CONNECTION_EVENT_TYPE = 6;
pub const QUIC_CONNECTION_EVENT_TYPE_QUIC_CONNECTION_EVENT_STREAMS_AVAILABLE:
    QUIC_CONNECTION_EVENT_TYPE = 7;
pub const QUIC_CONNECTION_EVENT_TYPE_QUIC_CONNECTION_EVENT_PEER_NEEDS_STREAMS:
    QUIC_CONNECTION_EVENT_TYPE = 8;
pub const QUIC_CONNECTION_EVENT_TYPE_QUIC_CONNECTION_EVENT_IDEAL_PROCESSOR_CHANGED:
    QUIC_CONNECTION_EVENT_TYPE = 9;
pub const QUIC_CONNECTION_EVENT_TYPE_QUIC_CONNECTION_EVENT_DATAGRAM_STATE_CHANGED:
    QUIC_CONNECTION_EVENT_TYPE = 10;
pub const QUIC_CONNECTION_EVENT_TYPE_QUIC_CONNECTION_EVENT_DATAGRAM_RECEIVED:
    QUIC_CONNECTION_EVENT_TYPE = 11;
pub const QUIC_CONNECTION_EVENT_TYPE_QUIC_CONNECTION_EVENT_DATAGRAM_SEND_STATE_CHANGED:
    QUIC_CONNECTION_EVENT_TYPE = 12;
pub const QUIC_CONNECTION_EVENT_TYPE_QUIC_CONNECTION_EVENT_RESUMED: QUIC_CONNECTION_EVENT_TYPE = 13;
pub const QUIC_CONNECTION_EVENT_TYPE_QUIC_CONNECTION_EVENT_RESUMPTION_TICKET_RECEIVED:
    QUIC_CONNECTION_EVENT_TYPE = 14;
pub const QUIC_CONNECTION_EVENT_TYPE_QUIC_CONNECTION_EVENT_PEER_CERTIFICATE_RECEIVED:
    QUIC_CONNECTION_EVENT_TYPE = 15;
pub const QUIC_CONNECTION_EVENT_TYPE_QUIC_CONNECTION_EVENT_RELIABLE_RESET_NEGOTIATED:
    QUIC_CONNECTION_EVENT_TYPE = 16;
pub const QUIC_CONNECTION_EVENT_TYPE_QUIC_CONNECTION_EVENT_ONE_WAY_DELAY_NEGOTIATED:
    QUIC_CONNECTION_EVENT_TYPE = 17;
pub const QUIC_CONNECTION_EVENT_TYPE_QUIC_CONNECTION_EVENT_NETWORK_STATISTICS:
    QUIC_CONNECTION_EVENT_TYPE = 18;
pub type QUIC_CONNECTION_EVENT_TYPE = ::std::os::raw::c_int;
#[repr(C)]
#[derive(Copy, Clone)]
pub struct QUIC_CONNECTION_EVENT {
    pub Type: QUIC_CONNECTION_EVENT_TYPE,
    pub __bindgen_anon_1: QUIC_CONNECTION_EVENT__bindgen_ty_1,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union QUIC_CONNECTION_EVENT__bindgen_ty_1 {
    pub CONNECTED: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_1,
    pub SHUTDOWN_INITIATED_BY_TRANSPORT: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_2,
    pub SHUTDOWN_INITIATED_BY_PEER: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_3,
    pub SHUTDOWN_COMPLETE: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_4,
    pub LOCAL_ADDRESS_CHANGED: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_5,
    pub PEER_ADDRESS_CHANGED: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_6,
    pub PEER_STREAM_STARTED: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_7,
    pub STREAMS_AVAILABLE: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_8,
    pub PEER_NEEDS_STREAMS: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_9,
    pub IDEAL_PROCESSOR_CHANGED: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_10,
    pub DATAGRAM_STATE_CHANGED: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_11,
    pub DATAGRAM_RECEIVED: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_12,
    pub DATAGRAM_SEND_STATE_CHANGED: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_13,
    pub RESUMED: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_14,
    pub RESUMPTION_TICKET_RECEIVED: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_15,
    pub PEER_CERTIFICATE_RECEIVED: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_16,
    pub RELIABLE_RESET_NEGOTIATED: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_17,
    pub ONE_WAY_DELAY_NEGOTIATED: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_18,
    pub NETWORK_STATISTICS: QUIC_NETWORK_STATISTICS,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_1 {
    pub SessionResumed: BOOLEAN,
    pub NegotiatedAlpnLength: u8,
    pub NegotiatedAlpn: *const u8,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_1"]
        [::std::mem::size_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_1>() - 16usize];
    ["Alignment of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_1"]
        [::std::mem::align_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_1>() - 8usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_1::SessionResumed"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_1,
        SessionResumed
    )
        - 0usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_1::NegotiatedAlpnLength"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_1,
        NegotiatedAlpnLength
    )
        - 1usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_1::NegotiatedAlpn"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_1,
        NegotiatedAlpn
    )
        - 8usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_2 {
    pub Status: HRESULT,
    pub ErrorCode: QUIC_UINT62,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_2"]
        [::std::mem::size_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_2>() - 16usize];
    ["Alignment of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_2"]
        [::std::mem::align_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_2>() - 8usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_2::Status"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_2,
        Status
    ) - 0usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_2::ErrorCode"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_2,
        ErrorCode
    ) - 8usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_3 {
    pub ErrorCode: QUIC_UINT62,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_3"]
        [::std::mem::size_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_3>() - 8usize];
    ["Alignment of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_3"]
        [::std::mem::align_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_3>() - 8usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_3::ErrorCode"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_3,
        ErrorCode
    ) - 0usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_4 {
    pub _bitfield_align_1: [u8; 0],
    pub _bitfield_1: __BindgenBitfieldUnit<[u8; 1usize]>,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_4"]
        [::std::mem::size_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_4>() - 1usize];
    ["Alignment of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_4"]
        [::std::mem::align_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_4>() - 1usize];
};
impl QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_4 {
    #[inline]
    pub fn HandshakeCompleted(&self) -> BOOLEAN {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(0usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_HandshakeCompleted(&mut self, val: BOOLEAN) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(0usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn HandshakeCompleted_raw(this: *const Self) -> BOOLEAN {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                0usize,
                1u8,
            ) as u8)
        }
    }
    #[inline]
    pub unsafe fn set_HandshakeCompleted_raw(this: *mut Self, val: BOOLEAN) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                0usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn PeerAcknowledgedShutdown(&self) -> BOOLEAN {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(1usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_PeerAcknowledgedShutdown(&mut self, val: BOOLEAN) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(1usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn PeerAcknowledgedShutdown_raw(this: *const Self) -> BOOLEAN {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                1usize,
                1u8,
            ) as u8)
        }
    }
    #[inline]
    pub unsafe fn set_PeerAcknowledgedShutdown_raw(this: *mut Self, val: BOOLEAN) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                1usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn AppCloseInProgress(&self) -> BOOLEAN {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(2usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_AppCloseInProgress(&mut self, val: BOOLEAN) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(2usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn AppCloseInProgress_raw(this: *const Self) -> BOOLEAN {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                2usize,
                1u8,
            ) as u8)
        }
    }
    #[inline]
    pub unsafe fn set_AppCloseInProgress_raw(this: *mut Self, val: BOOLEAN) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                2usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn new_bitfield_1(
        HandshakeCompleted: BOOLEAN,
        PeerAcknowledgedShutdown: BOOLEAN,
        AppCloseInProgress: BOOLEAN,
    ) -> __BindgenBitfieldUnit<[u8; 1usize]> {
        let mut __bindgen_bitfield_unit: __BindgenBitfieldUnit<[u8; 1usize]> = Default::default();
        __bindgen_bitfield_unit.set(0usize, 1u8, {
            let HandshakeCompleted: u8 = unsafe { ::std::mem::transmute(HandshakeCompleted) };
            HandshakeCompleted as u64
        });
        __bindgen_bitfield_unit.set(1usize, 1u8, {
            let PeerAcknowledgedShutdown: u8 =
                unsafe { ::std::mem::transmute(PeerAcknowledgedShutdown) };
            PeerAcknowledgedShutdown as u64
        });
        __bindgen_bitfield_unit.set(2usize, 1u8, {
            let AppCloseInProgress: u8 = unsafe { ::std::mem::transmute(AppCloseInProgress) };
            AppCloseInProgress as u64
        });
        __bindgen_bitfield_unit
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_5 {
    pub Address: *const QUIC_ADDR,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_5"]
        [::std::mem::size_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_5>() - 8usize];
    ["Alignment of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_5"]
        [::std::mem::align_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_5>() - 8usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_5::Address"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_5,
        Address
    ) - 0usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_6 {
    pub Address: *const QUIC_ADDR,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_6"]
        [::std::mem::size_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_6>() - 8usize];
    ["Alignment of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_6"]
        [::std::mem::align_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_6>() - 8usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_6::Address"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_6,
        Address
    ) - 0usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_7 {
    pub Stream: HQUIC,
    pub Flags: QUIC_STREAM_OPEN_FLAGS,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_7"]
        [::std::mem::size_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_7>() - 16usize];
    ["Alignment of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_7"]
        [::std::mem::align_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_7>() - 8usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_7::Stream"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_7,
        Stream
    ) - 0usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_7::Flags"]
        [::std::mem::offset_of!(QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_7, Flags) - 8usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_8 {
    pub BidirectionalCount: u16,
    pub UnidirectionalCount: u16,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_8"]
        [::std::mem::size_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_8>() - 4usize];
    ["Alignment of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_8"]
        [::std::mem::align_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_8>() - 2usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_8::BidirectionalCount"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_8,
        BidirectionalCount
    )
        - 0usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_8::UnidirectionalCount"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_8,
        UnidirectionalCount
    )
        - 2usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_9 {
    pub Bidirectional: BOOLEAN,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_9"]
        [::std::mem::size_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_9>() - 1usize];
    ["Alignment of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_9"]
        [::std::mem::align_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_9>() - 1usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_9::Bidirectional"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_9,
        Bidirectional
    )
        - 0usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_10 {
    pub IdealProcessor: u16,
    pub PartitionIndex: u16,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_10"]
        [::std::mem::size_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_10>() - 4usize];
    ["Alignment of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_10"]
        [::std::mem::align_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_10>() - 2usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_10::IdealProcessor"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_10,
        IdealProcessor
    )
        - 0usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_10::PartitionIndex"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_10,
        PartitionIndex
    )
        - 2usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_11 {
    pub SendEnabled: BOOLEAN,
    pub MaxSendLength: u16,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_11"]
        [::std::mem::size_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_11>() - 4usize];
    ["Alignment of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_11"]
        [::std::mem::align_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_11>() - 2usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_11::SendEnabled"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_11,
        SendEnabled
    )
        - 0usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_11::MaxSendLength"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_11,
        MaxSendLength
    )
        - 2usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_12 {
    pub Buffer: *const QUIC_BUFFER,
    pub Flags: QUIC_RECEIVE_FLAGS,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_12"]
        [::std::mem::size_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_12>() - 16usize];
    ["Alignment of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_12"]
        [::std::mem::align_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_12>() - 8usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_12::Buffer"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_12,
        Buffer
    ) - 0usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_12::Flags"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_12,
        Flags
    ) - 8usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_13 {
    pub ClientContext: *mut ::std::os::raw::c_void,
    pub State: QUIC_DATAGRAM_SEND_STATE,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_13"]
        [::std::mem::size_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_13>() - 16usize];
    ["Alignment of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_13"]
        [::std::mem::align_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_13>() - 8usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_13::ClientContext"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_13,
        ClientContext
    )
        - 0usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_13::State"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_13,
        State
    ) - 8usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_14 {
    pub ResumptionStateLength: u16,
    pub ResumptionState: *const u8,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_14"]
        [::std::mem::size_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_14>() - 16usize];
    ["Alignment of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_14"]
        [::std::mem::align_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_14>() - 8usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_14::ResumptionStateLength"] [:: std :: mem :: offset_of ! (QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_14 , ResumptionStateLength) - 0usize] ;
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_14::ResumptionState"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_14,
        ResumptionState
    )
        - 8usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_15 {
    pub ResumptionTicketLength: u32,
    pub ResumptionTicket: *const u8,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_15"]
        [::std::mem::size_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_15>() - 16usize];
    ["Alignment of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_15"]
        [::std::mem::align_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_15>() - 8usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_15::ResumptionTicketLength"] [:: std :: mem :: offset_of ! (QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_15 , ResumptionTicketLength) - 0usize] ;
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_15::ResumptionTicket"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_15,
        ResumptionTicket
    )
        - 8usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_16 {
    pub Certificate: *mut QUIC_CERTIFICATE,
    pub DeferredErrorFlags: u32,
    pub DeferredStatus: HRESULT,
    pub Chain: *mut QUIC_CERTIFICATE_CHAIN,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_16"]
        [::std::mem::size_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_16>() - 24usize];
    ["Alignment of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_16"]
        [::std::mem::align_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_16>() - 8usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_16::Certificate"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_16,
        Certificate
    )
        - 0usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_16::DeferredErrorFlags"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_16,
        DeferredErrorFlags
    )
        - 8usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_16::DeferredStatus"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_16,
        DeferredStatus
    )
        - 12usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_16::Chain"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_16,
        Chain
    ) - 16usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_17 {
    pub IsNegotiated: BOOLEAN,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_17"]
        [::std::mem::size_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_17>() - 1usize];
    ["Alignment of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_17"]
        [::std::mem::align_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_17>() - 1usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_17::IsNegotiated"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_17,
        IsNegotiated
    )
        - 0usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_18 {
    pub SendNegotiated: BOOLEAN,
    pub ReceiveNegotiated: BOOLEAN,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_18"]
        [::std::mem::size_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_18>() - 2usize];
    ["Alignment of QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_18"]
        [::std::mem::align_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_18>() - 1usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_18::SendNegotiated"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_18,
        SendNegotiated
    )
        - 0usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_18::ReceiveNegotiated"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1__bindgen_ty_18,
        ReceiveNegotiated
    )
        - 1usize];
};
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_CONNECTION_EVENT__bindgen_ty_1"]
        [::std::mem::size_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1>() - 48usize];
    ["Alignment of QUIC_CONNECTION_EVENT__bindgen_ty_1"]
        [::std::mem::align_of::<QUIC_CONNECTION_EVENT__bindgen_ty_1>() - 8usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1::CONNECTED"]
        [::std::mem::offset_of!(QUIC_CONNECTION_EVENT__bindgen_ty_1, CONNECTED) - 0usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1::SHUTDOWN_INITIATED_BY_TRANSPORT"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1,
        SHUTDOWN_INITIATED_BY_TRANSPORT
    )
        - 0usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1::SHUTDOWN_INITIATED_BY_PEER"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1,
        SHUTDOWN_INITIATED_BY_PEER
    )
        - 0usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1::SHUTDOWN_COMPLETE"]
        [::std::mem::offset_of!(QUIC_CONNECTION_EVENT__bindgen_ty_1, SHUTDOWN_COMPLETE) - 0usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1::LOCAL_ADDRESS_CHANGED"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1,
        LOCAL_ADDRESS_CHANGED
    ) - 0usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1::PEER_ADDRESS_CHANGED"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1,
        PEER_ADDRESS_CHANGED
    ) - 0usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1::PEER_STREAM_STARTED"]
        [::std::mem::offset_of!(QUIC_CONNECTION_EVENT__bindgen_ty_1, PEER_STREAM_STARTED) - 0usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1::STREAMS_AVAILABLE"]
        [::std::mem::offset_of!(QUIC_CONNECTION_EVENT__bindgen_ty_1, STREAMS_AVAILABLE) - 0usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1::PEER_NEEDS_STREAMS"]
        [::std::mem::offset_of!(QUIC_CONNECTION_EVENT__bindgen_ty_1, PEER_NEEDS_STREAMS) - 0usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1::IDEAL_PROCESSOR_CHANGED"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1,
        IDEAL_PROCESSOR_CHANGED
    ) - 0usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1::DATAGRAM_STATE_CHANGED"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1,
        DATAGRAM_STATE_CHANGED
    ) - 0usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1::DATAGRAM_RECEIVED"]
        [::std::mem::offset_of!(QUIC_CONNECTION_EVENT__bindgen_ty_1, DATAGRAM_RECEIVED) - 0usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1::DATAGRAM_SEND_STATE_CHANGED"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1,
        DATAGRAM_SEND_STATE_CHANGED
    )
        - 0usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1::RESUMED"]
        [::std::mem::offset_of!(QUIC_CONNECTION_EVENT__bindgen_ty_1, RESUMED) - 0usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1::RESUMPTION_TICKET_RECEIVED"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1,
        RESUMPTION_TICKET_RECEIVED
    )
        - 0usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1::PEER_CERTIFICATE_RECEIVED"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1,
        PEER_CERTIFICATE_RECEIVED
    ) - 0usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1::RELIABLE_RESET_NEGOTIATED"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1,
        RELIABLE_RESET_NEGOTIATED
    ) - 0usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1::ONE_WAY_DELAY_NEGOTIATED"][::std::mem::offset_of!(
        QUIC_CONNECTION_EVENT__bindgen_ty_1,
        ONE_WAY_DELAY_NEGOTIATED
    ) - 0usize];
    ["Offset of field: QUIC_CONNECTION_EVENT__bindgen_ty_1::NETWORK_STATISTICS"]
        [::std::mem::offset_of!(QUIC_CONNECTION_EVENT__bindgen_ty_1, NETWORK_STATISTICS) - 0usize];
};
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_CONNECTION_EVENT"][::std::mem::size_of::<QUIC_CONNECTION_EVENT>() - 56usize];
    ["Alignment of QUIC_CONNECTION_EVENT"]
        [::std::mem::align_of::<QUIC_CONNECTION_EVENT>() - 8usize];
    ["Offset of field: QUIC_CONNECTION_EVENT::Type"]
        [::std::mem::offset_of!(QUIC_CONNECTION_EVENT, Type) - 0usize];
};
pub type QUIC_CONNECTION_CALLBACK_HANDLER = ::std::option::Option<
    unsafe extern "C" fn(
        arg1: HQUIC,
        arg2: *mut ::std::os::raw::c_void,
        arg3: *mut QUIC_CONNECTION_EVENT,
    ) -> HRESULT,
>;
pub type QUIC_CONNECTION_OPEN_FN = ::std::option::Option<
    unsafe extern "C" fn(
        Registration: HQUIC,
        Handler: QUIC_CONNECTION_CALLBACK_HANDLER,
        Context: *mut ::std::os::raw::c_void,
        Connection: *mut HQUIC,
    ) -> HRESULT,
>;
pub type QUIC_CONNECTION_OPEN_IN_PARTITION_FN = ::std::option::Option<
    unsafe extern "C" fn(
        Registration: HQUIC,
        PartitionIndex: u16,
        Handler: QUIC_CONNECTION_CALLBACK_HANDLER,
        Context: *mut ::std::os::raw::c_void,
        Connection: *mut HQUIC,
    ) -> HRESULT,
>;
pub type QUIC_CONNECTION_CLOSE_FN = ::std::option::Option<unsafe extern "C" fn(Connection: HQUIC)>;
pub type QUIC_CONNECTION_SHUTDOWN_FN = ::std::option::Option<
    unsafe extern "C" fn(
        Connection: HQUIC,
        Flags: QUIC_CONNECTION_SHUTDOWN_FLAGS,
        ErrorCode: QUIC_UINT62,
    ),
>;
pub type QUIC_CONNECTION_START_FN = ::std::option::Option<
    unsafe extern "C" fn(
        Connection: HQUIC,
        Configuration: HQUIC,
        Family: QUIC_ADDRESS_FAMILY,
        ServerName: *const ::std::os::raw::c_char,
        ServerPort: u16,
    ) -> HRESULT,
>;
pub type QUIC_CONNECTION_SET_CONFIGURATION_FN =
    ::std::option::Option<unsafe extern "C" fn(Connection: HQUIC, Configuration: HQUIC) -> HRESULT>;
pub type QUIC_CONNECTION_SEND_RESUMPTION_FN = ::std::option::Option<
    unsafe extern "C" fn(
        Connection: HQUIC,
        Flags: QUIC_SEND_RESUMPTION_FLAGS,
        DataLength: u16,
        ResumptionData: *const u8,
    ) -> HRESULT,
>;
pub type QUIC_CONNECTION_COMP_RESUMPTION_FN =
    ::std::option::Option<unsafe extern "C" fn(Connection: HQUIC, Result: BOOLEAN) -> HRESULT>;
pub type QUIC_CONNECTION_COMP_CERT_FN = ::std::option::Option<
    unsafe extern "C" fn(
        Connection: HQUIC,
        Result: BOOLEAN,
        TlsAlert: QUIC_TLS_ALERT_CODES,
    ) -> HRESULT,
>;
pub const QUIC_STREAM_EVENT_TYPE_QUIC_STREAM_EVENT_START_COMPLETE: QUIC_STREAM_EVENT_TYPE = 0;
pub const QUIC_STREAM_EVENT_TYPE_QUIC_STREAM_EVENT_RECEIVE: QUIC_STREAM_EVENT_TYPE = 1;
pub const QUIC_STREAM_EVENT_TYPE_QUIC_STREAM_EVENT_SEND_COMPLETE: QUIC_STREAM_EVENT_TYPE = 2;
pub const QUIC_STREAM_EVENT_TYPE_QUIC_STREAM_EVENT_PEER_SEND_SHUTDOWN: QUIC_STREAM_EVENT_TYPE = 3;
pub const QUIC_STREAM_EVENT_TYPE_QUIC_STREAM_EVENT_PEER_SEND_ABORTED: QUIC_STREAM_EVENT_TYPE = 4;
pub const QUIC_STREAM_EVENT_TYPE_QUIC_STREAM_EVENT_PEER_RECEIVE_ABORTED: QUIC_STREAM_EVENT_TYPE = 5;
pub const QUIC_STREAM_EVENT_TYPE_QUIC_STREAM_EVENT_SEND_SHUTDOWN_COMPLETE: QUIC_STREAM_EVENT_TYPE =
    6;
pub const QUIC_STREAM_EVENT_TYPE_QUIC_STREAM_EVENT_SHUTDOWN_COMPLETE: QUIC_STREAM_EVENT_TYPE = 7;
pub const QUIC_STREAM_EVENT_TYPE_QUIC_STREAM_EVENT_IDEAL_SEND_BUFFER_SIZE: QUIC_STREAM_EVENT_TYPE =
    8;
pub const QUIC_STREAM_EVENT_TYPE_QUIC_STREAM_EVENT_PEER_ACCEPTED: QUIC_STREAM_EVENT_TYPE = 9;
pub const QUIC_STREAM_EVENT_TYPE_QUIC_STREAM_EVENT_CANCEL_ON_LOSS: QUIC_STREAM_EVENT_TYPE = 10;
pub type QUIC_STREAM_EVENT_TYPE = ::std::os::raw::c_int;
#[repr(C)]
#[derive(Copy, Clone)]
pub struct QUIC_STREAM_EVENT {
    pub Type: QUIC_STREAM_EVENT_TYPE,
    pub __bindgen_anon_1: QUIC_STREAM_EVENT__bindgen_ty_1,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union QUIC_STREAM_EVENT__bindgen_ty_1 {
    pub START_COMPLETE: QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_1,
    pub RECEIVE: QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_2,
    pub SEND_COMPLETE: QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_3,
    pub PEER_SEND_ABORTED: QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_4,
    pub PEER_RECEIVE_ABORTED: QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_5,
    pub SEND_SHUTDOWN_COMPLETE: QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_6,
    pub SHUTDOWN_COMPLETE: QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_7,
    pub IDEAL_SEND_BUFFER_SIZE: QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_8,
    pub CANCEL_ON_LOSS: QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_9,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_1 {
    pub Status: HRESULT,
    pub ID: QUIC_UINT62,
    pub _bitfield_align_1: [u8; 0],
    pub _bitfield_1: __BindgenBitfieldUnit<[u8; 1usize]>,
    pub __bindgen_padding_0: [u8; 7usize],
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_1"]
        [::std::mem::size_of::<QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_1>() - 24usize];
    ["Alignment of QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_1"]
        [::std::mem::align_of::<QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_1>() - 8usize];
    ["Offset of field: QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_1::Status"]
        [::std::mem::offset_of!(QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_1, Status) - 0usize];
    ["Offset of field: QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_1::ID"]
        [::std::mem::offset_of!(QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_1, ID) - 8usize];
};
impl QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_1 {
    #[inline]
    pub fn PeerAccepted(&self) -> BOOLEAN {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(0usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_PeerAccepted(&mut self, val: BOOLEAN) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(0usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn PeerAccepted_raw(this: *const Self) -> BOOLEAN {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                0usize,
                1u8,
            ) as u8)
        }
    }
    #[inline]
    pub unsafe fn set_PeerAccepted_raw(this: *mut Self, val: BOOLEAN) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                0usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn RESERVED(&self) -> BOOLEAN {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(1usize, 7u8) as u8) }
    }
    #[inline]
    pub fn set_RESERVED(&mut self, val: BOOLEAN) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(1usize, 7u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn RESERVED_raw(this: *const Self) -> BOOLEAN {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                1usize,
                7u8,
            ) as u8)
        }
    }
    #[inline]
    pub unsafe fn set_RESERVED_raw(this: *mut Self, val: BOOLEAN) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                1usize,
                7u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn new_bitfield_1(
        PeerAccepted: BOOLEAN,
        RESERVED: BOOLEAN,
    ) -> __BindgenBitfieldUnit<[u8; 1usize]> {
        let mut __bindgen_bitfield_unit: __BindgenBitfieldUnit<[u8; 1usize]> = Default::default();
        __bindgen_bitfield_unit.set(0usize, 1u8, {
            let PeerAccepted: u8 = unsafe { ::std::mem::transmute(PeerAccepted) };
            PeerAccepted as u64
        });
        __bindgen_bitfield_unit.set(1usize, 7u8, {
            let RESERVED: u8 = unsafe { ::std::mem::transmute(RESERVED) };
            RESERVED as u64
        });
        __bindgen_bitfield_unit
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_2 {
    pub AbsoluteOffset: u64,
    pub TotalBufferLength: u64,
    pub Buffers: *const QUIC_BUFFER,
    pub BufferCount: u32,
    pub Flags: QUIC_RECEIVE_FLAGS,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_2"]
        [::std::mem::size_of::<QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_2>() - 32usize];
    ["Alignment of QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_2"]
        [::std::mem::align_of::<QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_2>() - 8usize];
    ["Offset of field: QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_2::AbsoluteOffset"][::std::mem::offset_of!(
        QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_2,
        AbsoluteOffset
    ) - 0usize];
    ["Offset of field: QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_2::TotalBufferLength"][::std::mem::offset_of!(
        QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_2,
        TotalBufferLength
    )
        - 8usize];
    ["Offset of field: QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_2::Buffers"]
        [::std::mem::offset_of!(QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_2, Buffers) - 16usize];
    ["Offset of field: QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_2::BufferCount"][::std::mem::offset_of!(
        QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_2,
        BufferCount
    ) - 24usize];
    ["Offset of field: QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_2::Flags"]
        [::std::mem::offset_of!(QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_2, Flags) - 28usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_3 {
    pub Canceled: BOOLEAN,
    pub ClientContext: *mut ::std::os::raw::c_void,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_3"]
        [::std::mem::size_of::<QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_3>() - 16usize];
    ["Alignment of QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_3"]
        [::std::mem::align_of::<QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_3>() - 8usize];
    ["Offset of field: QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_3::Canceled"]
        [::std::mem::offset_of!(QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_3, Canceled) - 0usize];
    ["Offset of field: QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_3::ClientContext"][::std::mem::offset_of!(
        QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_3,
        ClientContext
    ) - 8usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_4 {
    pub ErrorCode: QUIC_UINT62,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_4"]
        [::std::mem::size_of::<QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_4>() - 8usize];
    ["Alignment of QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_4"]
        [::std::mem::align_of::<QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_4>() - 8usize];
    ["Offset of field: QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_4::ErrorCode"]
        [::std::mem::offset_of!(QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_4, ErrorCode) - 0usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_5 {
    pub ErrorCode: QUIC_UINT62,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_5"]
        [::std::mem::size_of::<QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_5>() - 8usize];
    ["Alignment of QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_5"]
        [::std::mem::align_of::<QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_5>() - 8usize];
    ["Offset of field: QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_5::ErrorCode"]
        [::std::mem::offset_of!(QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_5, ErrorCode) - 0usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_6 {
    pub Graceful: BOOLEAN,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_6"]
        [::std::mem::size_of::<QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_6>() - 1usize];
    ["Alignment of QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_6"]
        [::std::mem::align_of::<QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_6>() - 1usize];
    ["Offset of field: QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_6::Graceful"]
        [::std::mem::offset_of!(QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_6, Graceful) - 0usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_7 {
    pub ConnectionShutdown: BOOLEAN,
    pub _bitfield_align_1: [u8; 0],
    pub _bitfield_1: __BindgenBitfieldUnit<[u8; 1usize]>,
    pub ConnectionErrorCode: QUIC_UINT62,
    pub ConnectionCloseStatus: HRESULT,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_7"]
        [::std::mem::size_of::<QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_7>() - 24usize];
    ["Alignment of QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_7"]
        [::std::mem::align_of::<QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_7>() - 8usize];
    ["Offset of field: QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_7::ConnectionShutdown"][::std::mem::offset_of!(
        QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_7,
        ConnectionShutdown
    )
        - 0usize];
    ["Offset of field: QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_7::ConnectionErrorCode"][::std::mem::offset_of!(
        QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_7,
        ConnectionErrorCode
    )
        - 8usize];
    ["Offset of field: QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_7::ConnectionCloseStatus"][::std::mem::offset_of!(
        QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_7,
        ConnectionCloseStatus
    )
        - 16usize];
};
impl QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_7 {
    #[inline]
    pub fn AppCloseInProgress(&self) -> BOOLEAN {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(0usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_AppCloseInProgress(&mut self, val: BOOLEAN) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(0usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn AppCloseInProgress_raw(this: *const Self) -> BOOLEAN {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                0usize,
                1u8,
            ) as u8)
        }
    }
    #[inline]
    pub unsafe fn set_AppCloseInProgress_raw(this: *mut Self, val: BOOLEAN) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                0usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn ConnectionShutdownByApp(&self) -> BOOLEAN {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(1usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_ConnectionShutdownByApp(&mut self, val: BOOLEAN) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(1usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn ConnectionShutdownByApp_raw(this: *const Self) -> BOOLEAN {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                1usize,
                1u8,
            ) as u8)
        }
    }
    #[inline]
    pub unsafe fn set_ConnectionShutdownByApp_raw(this: *mut Self, val: BOOLEAN) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                1usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn ConnectionClosedRemotely(&self) -> BOOLEAN {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(2usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_ConnectionClosedRemotely(&mut self, val: BOOLEAN) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(2usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn ConnectionClosedRemotely_raw(this: *const Self) -> BOOLEAN {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                2usize,
                1u8,
            ) as u8)
        }
    }
    #[inline]
    pub unsafe fn set_ConnectionClosedRemotely_raw(this: *mut Self, val: BOOLEAN) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                2usize,
                1u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn RESERVED(&self) -> BOOLEAN {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(3usize, 5u8) as u8) }
    }
    #[inline]
    pub fn set_RESERVED(&mut self, val: BOOLEAN) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(3usize, 5u8, val as u64)
        }
    }
    #[inline]
    pub unsafe fn RESERVED_raw(this: *const Self) -> BOOLEAN {
        unsafe {
            ::std::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(
                ::std::ptr::addr_of!((*this)._bitfield_1),
                3usize,
                5u8,
            ) as u8)
        }
    }
    #[inline]
    pub unsafe fn set_RESERVED_raw(this: *mut Self, val: BOOLEAN) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            <__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(
                ::std::ptr::addr_of_mut!((*this)._bitfield_1),
                3usize,
                5u8,
                val as u64,
            )
        }
    }
    #[inline]
    pub fn new_bitfield_1(
        AppCloseInProgress: BOOLEAN,
        ConnectionShutdownByApp: BOOLEAN,
        ConnectionClosedRemotely: BOOLEAN,
        RESERVED: BOOLEAN,
    ) -> __BindgenBitfieldUnit<[u8; 1usize]> {
        let mut __bindgen_bitfield_unit: __BindgenBitfieldUnit<[u8; 1usize]> = Default::default();
        __bindgen_bitfield_unit.set(0usize, 1u8, {
            let AppCloseInProgress: u8 = unsafe { ::std::mem::transmute(AppCloseInProgress) };
            AppCloseInProgress as u64
        });
        __bindgen_bitfield_unit.set(1usize, 1u8, {
            let ConnectionShutdownByApp: u8 =
                unsafe { ::std::mem::transmute(ConnectionShutdownByApp) };
            ConnectionShutdownByApp as u64
        });
        __bindgen_bitfield_unit.set(2usize, 1u8, {
            let ConnectionClosedRemotely: u8 =
                unsafe { ::std::mem::transmute(ConnectionClosedRemotely) };
            ConnectionClosedRemotely as u64
        });
        __bindgen_bitfield_unit.set(3usize, 5u8, {
            let RESERVED: u8 = unsafe { ::std::mem::transmute(RESERVED) };
            RESERVED as u64
        });
        __bindgen_bitfield_unit
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_8 {
    pub ByteCount: u64,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_8"]
        [::std::mem::size_of::<QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_8>() - 8usize];
    ["Alignment of QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_8"]
        [::std::mem::align_of::<QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_8>() - 8usize];
    ["Offset of field: QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_8::ByteCount"]
        [::std::mem::offset_of!(QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_8, ByteCount) - 0usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_9 {
    pub ErrorCode: QUIC_UINT62,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_9"]
        [::std::mem::size_of::<QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_9>() - 8usize];
    ["Alignment of QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_9"]
        [::std::mem::align_of::<QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_9>() - 8usize];
    ["Offset of field: QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_9::ErrorCode"]
        [::std::mem::offset_of!(QUIC_STREAM_EVENT__bindgen_ty_1__bindgen_ty_9, ErrorCode) - 0usize];
};
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_STREAM_EVENT__bindgen_ty_1"]
        [::std::mem::size_of::<QUIC_STREAM_EVENT__bindgen_ty_1>() - 32usize];
    ["Alignment of QUIC_STREAM_EVENT__bindgen_ty_1"]
        [::std::mem::align_of::<QUIC_STREAM_EVENT__bindgen_ty_1>() - 8usize];
    ["Offset of field: QUIC_STREAM_EVENT__bindgen_ty_1::START_COMPLETE"]
        [::std::mem::offset_of!(QUIC_STREAM_EVENT__bindgen_ty_1, START_COMPLETE) - 0usize];
    ["Offset of field: QUIC_STREAM_EVENT__bindgen_ty_1::RECEIVE"]
        [::std::mem::offset_of!(QUIC_STREAM_EVENT__bindgen_ty_1, RECEIVE) - 0usize];
    ["Offset of field: QUIC_STREAM_EVENT__bindgen_ty_1::SEND_COMPLETE"]
        [::std::mem::offset_of!(QUIC_STREAM_EVENT__bindgen_ty_1, SEND_COMPLETE) - 0usize];
    ["Offset of field: QUIC_STREAM_EVENT__bindgen_ty_1::PEER_SEND_ABORTED"]
        [::std::mem::offset_of!(QUIC_STREAM_EVENT__bindgen_ty_1, PEER_SEND_ABORTED) - 0usize];
    ["Offset of field: QUIC_STREAM_EVENT__bindgen_ty_1::PEER_RECEIVE_ABORTED"]
        [::std::mem::offset_of!(QUIC_STREAM_EVENT__bindgen_ty_1, PEER_RECEIVE_ABORTED) - 0usize];
    ["Offset of field: QUIC_STREAM_EVENT__bindgen_ty_1::SEND_SHUTDOWN_COMPLETE"]
        [::std::mem::offset_of!(QUIC_STREAM_EVENT__bindgen_ty_1, SEND_SHUTDOWN_COMPLETE) - 0usize];
    ["Offset of field: QUIC_STREAM_EVENT__bindgen_ty_1::SHUTDOWN_COMPLETE"]
        [::std::mem::offset_of!(QUIC_STREAM_EVENT__bindgen_ty_1, SHUTDOWN_COMPLETE) - 0usize];
    ["Offset of field: QUIC_STREAM_EVENT__bindgen_ty_1::IDEAL_SEND_BUFFER_SIZE"]
        [::std::mem::offset_of!(QUIC_STREAM_EVENT__bindgen_ty_1, IDEAL_SEND_BUFFER_SIZE) - 0usize];
    ["Offset of field: QUIC_STREAM_EVENT__bindgen_ty_1::CANCEL_ON_LOSS"]
        [::std::mem::offset_of!(QUIC_STREAM_EVENT__bindgen_ty_1, CANCEL_ON_LOSS) - 0usize];
};
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_STREAM_EVENT"][::std::mem::size_of::<QUIC_STREAM_EVENT>() - 40usize];
    ["Alignment of QUIC_STREAM_EVENT"][::std::mem::align_of::<QUIC_STREAM_EVENT>() - 8usize];
    ["Offset of field: QUIC_STREAM_EVENT::Type"]
        [::std::mem::offset_of!(QUIC_STREAM_EVENT, Type) - 0usize];
};
pub type QUIC_STREAM_CALLBACK_HANDLER = ::std::option::Option<
    unsafe extern "C" fn(
        arg1: HQUIC,
        arg2: *mut ::std::os::raw::c_void,
        arg3: *mut QUIC_STREAM_EVENT,
    ) -> HRESULT,
>;
pub type QUIC_STREAM_OPEN_FN = ::std::option::Option<
    unsafe extern "C" fn(
        Connection: HQUIC,
        Flags: QUIC_STREAM_OPEN_FLAGS,
        Handler: QUIC_STREAM_CALLBACK_HANDLER,
        Context: *mut ::std::os::raw::c_void,
        Stream: *mut HQUIC,
    ) -> HRESULT,
>;
pub type QUIC_STREAM_CLOSE_FN = ::std::option::Option<unsafe extern "C" fn(Stream: HQUIC)>;
pub type QUIC_STREAM_START_FN = ::std::option::Option<
    unsafe extern "C" fn(Stream: HQUIC, Flags: QUIC_STREAM_START_FLAGS) -> HRESULT,
>;
pub type QUIC_STREAM_SHUTDOWN_FN = ::std::option::Option<
    unsafe extern "C" fn(
        Stream: HQUIC,
        Flags: QUIC_STREAM_SHUTDOWN_FLAGS,
        ErrorCode: QUIC_UINT62,
    ) -> HRESULT,
>;
pub type QUIC_STREAM_SEND_FN = ::std::option::Option<
    unsafe extern "C" fn(
        Stream: HQUIC,
        Buffers: *const QUIC_BUFFER,
        BufferCount: u32,
        Flags: QUIC_SEND_FLAGS,
        ClientSendContext: *mut ::std::os::raw::c_void,
    ) -> HRESULT,
>;
pub type QUIC_STREAM_RECEIVE_COMPLETE_FN =
    ::std::option::Option<unsafe extern "C" fn(Stream: HQUIC, BufferLength: u64)>;
pub type QUIC_STREAM_RECEIVE_SET_ENABLED_FN =
    ::std::option::Option<unsafe extern "C" fn(Stream: HQUIC, IsEnabled: BOOLEAN) -> HRESULT>;
pub type QUIC_STREAM_PROVIDE_RECEIVE_BUFFERS_FN = ::std::option::Option<
    unsafe extern "C" fn(Stream: HQUIC, BufferCount: u32, Buffers: *const QUIC_BUFFER) -> HRESULT,
>;
pub type QUIC_DATAGRAM_SEND_FN = ::std::option::Option<
    unsafe extern "C" fn(
        Connection: HQUIC,
        Buffers: *const QUIC_BUFFER,
        BufferCount: u32,
        Flags: QUIC_SEND_FLAGS,
        ClientSendContext: *mut ::std::os::raw::c_void,
    ) -> HRESULT,
>;
pub const QUIC_CONNECTION_POOL_FLAGS_QUIC_CONNECTION_POOL_FLAG_NONE: QUIC_CONNECTION_POOL_FLAGS = 0;
pub const QUIC_CONNECTION_POOL_FLAGS_QUIC_CONNECTION_POOL_FLAG_CLOSE_ON_FAILURE:
    QUIC_CONNECTION_POOL_FLAGS = 1;
pub type QUIC_CONNECTION_POOL_FLAGS = ::std::os::raw::c_int;
#[repr(C)]
pub struct QUIC_CONNECTION_POOL_CONFIG {
    pub Registration: HQUIC,
    pub Configuration: HQUIC,
    pub Handler: QUIC_CONNECTION_CALLBACK_HANDLER,
    pub Context: *mut *mut ::std::os::raw::c_void,
    pub ServerName: *const ::std::os::raw::c_char,
    pub ServerAddress: *const QUIC_ADDR,
    pub Family: QUIC_ADDRESS_FAMILY,
    pub ServerPort: u16,
    pub NumberOfConnections: u16,
    pub CibirIds: *mut *mut u8,
    pub CibirIdLength: u8,
    pub Flags: QUIC_CONNECTION_POOL_FLAGS,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_CONNECTION_POOL_CONFIG"]
        [::std::mem::size_of::<QUIC_CONNECTION_POOL_CONFIG>() - 72usize];
    ["Alignment of QUIC_CONNECTION_POOL_CONFIG"]
        [::std::mem::align_of::<QUIC_CONNECTION_POOL_CONFIG>() - 8usize];
    ["Offset of field: QUIC_CONNECTION_POOL_CONFIG::Registration"]
        [::std::mem::offset_of!(QUIC_CONNECTION_POOL_CONFIG, Registration) - 0usize];
    ["Offset of field: QUIC_CONNECTION_POOL_CONFIG::Configuration"]
        [::std::mem::offset_of!(QUIC_CONNECTION_POOL_CONFIG, Configuration) - 8usize];
    ["Offset of field: QUIC_CONNECTION_POOL_CONFIG::Handler"]
        [::std::mem::offset_of!(QUIC_CONNECTION_POOL_CONFIG, Handler) - 16usize];
    ["Offset of field: QUIC_CONNECTION_POOL_CONFIG::Context"]
        [::std::mem::offset_of!(QUIC_CONNECTION_POOL_CONFIG, Context) - 24usize];
    ["Offset of field: QUIC_CONNECTION_POOL_CONFIG::ServerName"]
        [::std::mem::offset_of!(QUIC_CONNECTION_POOL_CONFIG, ServerName) - 32usize];
    ["Offset of field: QUIC_CONNECTION_POOL_CONFIG::ServerAddress"]
        [::std::mem::offset_of!(QUIC_CONNECTION_POOL_CONFIG, ServerAddress) - 40usize];
    ["Offset of field: QUIC_CONNECTION_POOL_CONFIG::Family"]
        [::std::mem::offset_of!(QUIC_CONNECTION_POOL_CONFIG, Family) - 48usize];
    ["Offset of field: QUIC_CONNECTION_POOL_CONFIG::ServerPort"]
        [::std::mem::offset_of!(QUIC_CONNECTION_POOL_CONFIG, ServerPort) - 50usize];
    ["Offset of field: QUIC_CONNECTION_POOL_CONFIG::NumberOfConnections"]
        [::std::mem::offset_of!(QUIC_CONNECTION_POOL_CONFIG, NumberOfConnections) - 52usize];
    ["Offset of field: QUIC_CONNECTION_POOL_CONFIG::CibirIds"]
        [::std::mem::offset_of!(QUIC_CONNECTION_POOL_CONFIG, CibirIds) - 56usize];
    ["Offset of field: QUIC_CONNECTION_POOL_CONFIG::CibirIdLength"]
        [::std::mem::offset_of!(QUIC_CONNECTION_POOL_CONFIG, CibirIdLength) - 64usize];
    ["Offset of field: QUIC_CONNECTION_POOL_CONFIG::Flags"]
        [::std::mem::offset_of!(QUIC_CONNECTION_POOL_CONFIG, Flags) - 68usize];
};
pub type QUIC_CONN_POOL_CREATE_FN = ::std::option::Option<
    unsafe extern "C" fn(
        Config: *mut QUIC_CONNECTION_POOL_CONFIG,
        ConnectionPool: *mut HQUIC,
    ) -> HRESULT,
>;
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct QUIC_API_TABLE {
    pub SetContext: QUIC_SET_CONTEXT_FN,
    pub GetContext: QUIC_GET_CONTEXT_FN,
    pub SetCallbackHandler: QUIC_SET_CALLBACK_HANDLER_FN,
    pub SetParam: QUIC_SET_PARAM_FN,
    pub GetParam: QUIC_GET_PARAM_FN,
    pub RegistrationOpen: QUIC_REGISTRATION_OPEN_FN,
    pub RegistrationClose: QUIC_REGISTRATION_CLOSE_FN,
    pub RegistrationShutdown: QUIC_REGISTRATION_SHUTDOWN_FN,
    pub ConfigurationOpen: QUIC_CONFIGURATION_OPEN_FN,
    pub ConfigurationClose: QUIC_CONFIGURATION_CLOSE_FN,
    pub ConfigurationLoadCredential: QUIC_CONFIGURATION_LOAD_CREDENTIAL_FN,
    pub ListenerOpen: QUIC_LISTENER_OPEN_FN,
    pub ListenerClose: QUIC_LISTENER_CLOSE_FN,
    pub ListenerStart: QUIC_LISTENER_START_FN,
    pub ListenerStop: QUIC_LISTENER_STOP_FN,
    pub ConnectionOpen: QUIC_CONNECTION_OPEN_FN,
    pub ConnectionClose: QUIC_CONNECTION_CLOSE_FN,
    pub ConnectionShutdown: QUIC_CONNECTION_SHUTDOWN_FN,
    pub ConnectionStart: QUIC_CONNECTION_START_FN,
    pub ConnectionSetConfiguration: QUIC_CONNECTION_SET_CONFIGURATION_FN,
    pub ConnectionSendResumptionTicket: QUIC_CONNECTION_SEND_RESUMPTION_FN,
    pub StreamOpen: QUIC_STREAM_OPEN_FN,
    pub StreamClose: QUIC_STREAM_CLOSE_FN,
    pub StreamStart: QUIC_STREAM_START_FN,
    pub StreamShutdown: QUIC_STREAM_SHUTDOWN_FN,
    pub StreamSend: QUIC_STREAM_SEND_FN,
    pub StreamReceiveComplete: QUIC_STREAM_RECEIVE_COMPLETE_FN,
    pub StreamReceiveSetEnabled: QUIC_STREAM_RECEIVE_SET_ENABLED_FN,
    pub DatagramSend: QUIC_DATAGRAM_SEND_FN,
    pub ConnectionResumptionTicketValidationComplete: QUIC_CONNECTION_COMP_RESUMPTION_FN,
    pub ConnectionCertificateValidationComplete: QUIC_CONNECTION_COMP_CERT_FN,
    pub ConnectionOpenInPartition: QUIC_CONNECTION_OPEN_IN_PARTITION_FN,
    pub StreamProvideReceiveBuffers: QUIC_STREAM_PROVIDE_RECEIVE_BUFFERS_FN,
    pub ConnectionPoolCreate: QUIC_CONN_POOL_CREATE_FN,
    pub ExecutionCreate: QUIC_EXECUTION_CREATE_FN,
    pub ExecutionDelete: QUIC_EXECUTION_DELETE_FN,
    pub ExecutionPoll: QUIC_EXECUTION_POLL_FN,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of QUIC_API_TABLE"][::std::mem::size_of::<QUIC_API_TABLE>() - 296usize];
    ["Alignment of QUIC_API_TABLE"][::std::mem::align_of::<QUIC_API_TABLE>() - 8usize];
    ["Offset of field: QUIC_API_TABLE::SetContext"]
        [::std::mem::offset_of!(QUIC_API_TABLE, SetContext) - 0usize];
    ["Offset of field: QUIC_API_TABLE::GetContext"]
        [::std::mem::offset_of!(QUIC_API_TABLE, GetContext) - 8usize];
    ["Offset of field: QUIC_API_TABLE::SetCallbackHandler"]
        [::std::mem::offset_of!(QUIC_API_TABLE, SetCallbackHandler) - 16usize];
    ["Offset of field: QUIC_API_TABLE::SetParam"]
        [::std::mem::offset_of!(QUIC_API_TABLE, SetParam) - 24usize];
    ["Offset of field: QUIC_API_TABLE::GetParam"]
        [::std::mem::offset_of!(QUIC_API_TABLE, GetParam) - 32usize];
    ["Offset of field: QUIC_API_TABLE::RegistrationOpen"]
        [::std::mem::offset_of!(QUIC_API_TABLE, RegistrationOpen) - 40usize];
    ["Offset of field: QUIC_API_TABLE::RegistrationClose"]
        [::std::mem::offset_of!(QUIC_API_TABLE, RegistrationClose) - 48usize];
    ["Offset of field: QUIC_API_TABLE::RegistrationShutdown"]
        [::std::mem::offset_of!(QUIC_API_TABLE, RegistrationShutdown) - 56usize];
    ["Offset of field: QUIC_API_TABLE::ConfigurationOpen"]
        [::std::mem::offset_of!(QUIC_API_TABLE, ConfigurationOpen) - 64usize];
    ["Offset of field: QUIC_API_TABLE::ConfigurationClose"]
        [::std::mem::offset_of!(QUIC_API_TABLE, ConfigurationClose) - 72usize];
    ["Offset of field: QUIC_API_TABLE::ConfigurationLoadCredential"]
        [::std::mem::offset_of!(QUIC_API_TABLE, ConfigurationLoadCredential) - 80usize];
    ["Offset of field: QUIC_API_TABLE::ListenerOpen"]
        [::std::mem::offset_of!(QUIC_API_TABLE, ListenerOpen) - 88usize];
    ["Offset of field: QUIC_API_TABLE::ListenerClose"]
        [::std::mem::offset_of!(QUIC_API_TABLE, ListenerClose) - 96usize];
    ["Offset of field: QUIC_API_TABLE::ListenerStart"]
        [::std::mem::offset_of!(QUIC_API_TABLE, ListenerStart) - 104usize];
    ["Offset of field: QUIC_API_TABLE::ListenerStop"]
        [::std::mem::offset_of!(QUIC_API_TABLE, ListenerStop) - 112usize];
    ["Offset of field: QUIC_API_TABLE::ConnectionOpen"]
        [::std::mem::offset_of!(QUIC_API_TABLE, ConnectionOpen) - 120usize];
    ["Offset of field: QUIC_API_TABLE::ConnectionClose"]
        [::std::mem::offset_of!(QUIC_API_TABLE, ConnectionClose) - 128usize];
    ["Offset of field: QUIC_API_TABLE::ConnectionShutdown"]
        [::std::mem::offset_of!(QUIC_API_TABLE, ConnectionShutdown) - 136usize];
    ["Offset of field: QUIC_API_TABLE::ConnectionStart"]
        [::std::mem::offset_of!(QUIC_API_TABLE, ConnectionStart) - 144usize];
    ["Offset of field: QUIC_API_TABLE::ConnectionSetConfiguration"]
        [::std::mem::offset_of!(QUIC_API_TABLE, ConnectionSetConfiguration) - 152usize];
    ["Offset of field: QUIC_API_TABLE::ConnectionSendResumptionTicket"]
        [::std::mem::offset_of!(QUIC_API_TABLE, ConnectionSendResumptionTicket) - 160usize];
    ["Offset of field: QUIC_API_TABLE::StreamOpen"]
        [::std::mem::offset_of!(QUIC_API_TABLE, StreamOpen) - 168usize];
    ["Offset of field: QUIC_API_TABLE::StreamClose"]
        [::std::mem::offset_of!(QUIC_API_TABLE, StreamClose) - 176usize];
    ["Offset of field: QUIC_API_TABLE::StreamStart"]
        [::std::mem::offset_of!(QUIC_API_TABLE, StreamStart) - 184usize];
    ["Offset of field: QUIC_API_TABLE::StreamShutdown"]
        [::std::mem::offset_of!(QUIC_API_TABLE, StreamShutdown) - 192usize];
    ["Offset of field: QUIC_API_TABLE::StreamSend"]
        [::std::mem::offset_of!(QUIC_API_TABLE, StreamSend) - 200usize];
    ["Offset of field: QUIC_API_TABLE::StreamReceiveComplete"]
        [::std::mem::offset_of!(QUIC_API_TABLE, StreamReceiveComplete) - 208usize];
    ["Offset of field: QUIC_API_TABLE::StreamReceiveSetEnabled"]
        [::std::mem::offset_of!(QUIC_API_TABLE, StreamReceiveSetEnabled) - 216usize];
    ["Offset of field: QUIC_API_TABLE::DatagramSend"]
        [::std::mem::offset_of!(QUIC_API_TABLE, DatagramSend) - 224usize];
    ["Offset of field: QUIC_API_TABLE::ConnectionResumptionTicketValidationComplete"][::std::mem::offset_of!(
        QUIC_API_TABLE,
        ConnectionResumptionTicketValidationComplete
    ) - 232usize];
    ["Offset of field: QUIC_API_TABLE::ConnectionCertificateValidationComplete"][::std::mem::offset_of!(
        QUIC_API_TABLE,
        ConnectionCertificateValidationComplete
    ) - 240usize];
    ["Offset of field: QUIC_API_TABLE::ConnectionOpenInPartition"]
        [::std::mem::offset_of!(QUIC_API_TABLE, ConnectionOpenInPartition) - 248usize];
    ["Offset of field: QUIC_API_TABLE::StreamProvideReceiveBuffers"]
        [::std::mem::offset_of!(QUIC_API_TABLE, StreamProvideReceiveBuffers) - 256usize];
    ["Offset of field: QUIC_API_TABLE::ConnectionPoolCreate"]
        [::std::mem::offset_of!(QUIC_API_TABLE, ConnectionPoolCreate) - 264usize];
    ["Offset of field: QUIC_API_TABLE::ExecutionCreate"]
        [::std::mem::offset_of!(QUIC_API_TABLE, ExecutionCreate) - 272usize];
    ["Offset of field: QUIC_API_TABLE::ExecutionDelete"]
        [::std::mem::offset_of!(QUIC_API_TABLE, ExecutionDelete) - 280usize];
    ["Offset of field: QUIC_API_TABLE::ExecutionPoll"]
        [::std::mem::offset_of!(QUIC_API_TABLE, ExecutionPoll) - 288usize];
};
pub const QUIC_STATUS_SUCCESS: QUIC_STATUS = 0;
pub const QUIC_STATUS_PENDING: QUIC_STATUS = 459749;
pub const QUIC_STATUS_CONTINUE: QUIC_STATUS = 459998;
pub const QUIC_STATUS_OUT_OF_MEMORY: QUIC_STATUS = -**********;
pub const QUIC_STATUS_INVALID_PARAMETER: QUIC_STATUS = -**********;
pub const QUIC_STATUS_INVALID_STATE: QUIC_STATUS = -**********;
pub const QUIC_STATUS_NOT_SUPPORTED: QUIC_STATUS = -**********;
pub const QUIC_STATUS_NOT_FOUND: QUIC_STATUS = -2147023728;
pub const QUIC_STATUS_BUFFER_TOO_SMALL: QUIC_STATUS = -2147024774;
pub const QUIC_STATUS_HANDSHAKE_FAILURE: QUIC_STATUS = -2143223808;
pub const QUIC_STATUS_ABORTED: QUIC_STATUS = -2147467260;
pub const QUIC_STATUS_ADDRESS_IN_USE: QUIC_STATUS = -2147014848;
pub const QUIC_STATUS_INVALID_ADDRESS: QUIC_STATUS = -2147014847;
pub const QUIC_STATUS_CONNECTION_TIMEOUT: QUIC_STATUS = -2143223802;
pub const QUIC_STATUS_CONNECTION_IDLE: QUIC_STATUS = -2143223803;
pub const QUIC_STATUS_UNREACHABLE: QUIC_STATUS = -2147023664;
pub const QUIC_STATUS_INTERNAL_ERROR: QUIC_STATUS = -2143223805;
pub const QUIC_STATUS_CONNECTION_REFUSED: QUIC_STATUS = -2147023671;
pub const QUIC_STATUS_PROTOCOL_ERROR: QUIC_STATUS = -2143223804;
pub const QUIC_STATUS_VER_NEG_ERROR: QUIC_STATUS = -2143223807;
pub const QUIC_STATUS_TLS_ERROR: QUIC_STATUS = -2147013864;
pub const QUIC_STATUS_USER_CANCELED: QUIC_STATUS = -2143223806;
pub const QUIC_STATUS_ALPN_NEG_FAILURE: QUIC_STATUS = -2143223801;
pub const QUIC_STATUS_STREAM_LIMIT_REACHED: QUIC_STATUS = -2143223800;
pub const QUIC_STATUS_ALPN_IN_USE: QUIC_STATUS = -2143223799;
pub const QUIC_STATUS_CLOSE_NOTIFY: QUIC_STATUS = -2143223552;
pub const QUIC_STATUS_BAD_CERTIFICATE: QUIC_STATUS = -2143223510;
pub const QUIC_STATUS_UNSUPPORTED_CERTIFICATE: QUIC_STATUS = -2143223509;
pub const QUIC_STATUS_REVOKED_CERTIFICATE: QUIC_STATUS = -2143223508;
pub const QUIC_STATUS_EXPIRED_CERTIFICATE: QUIC_STATUS = -2143223507;
pub const QUIC_STATUS_UNKNOWN_CERTIFICATE: QUIC_STATUS = -2143223506;
pub const QUIC_STATUS_REQUIRED_CERTIFICATE: QUIC_STATUS = -2143223436;
pub const QUIC_STATUS_CERT_EXPIRED: QUIC_STATUS = -2146762495;
pub const QUIC_STATUS_CERT_UNTRUSTED_ROOT: QUIC_STATUS = -2146762487;
pub const QUIC_STATUS_CERT_NO_CERT: QUIC_STATUS = -2146893042;
pub type QUIC_STATUS = ::std::os::raw::c_int;
