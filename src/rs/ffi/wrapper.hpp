// header wrapper to feed into bindgen

#define QUIC_API_ENABLE_PREVIEW_FEATURES
#include "msquic.h"

// undef the macro type and define the enum type.
// This is ugly here but makes Rust code aligned with c code.
#undef QUIC_STATUS
typedef enum QUIC_STATUS {
#ifndef _WIN32
// on posix all status code relies on the macro type so we redefine it.
#define QUIC_STATUS unsigned int
#endif
    SUCCESS = QUIC_STATUS_SUCCESS,
    PENDING = QUIC_STATUS_PENDING,
    CONTINUE = QUIC_STATUS_CONTINUE,
    OUT_OF_MEMORY = QUIC_STATUS_OUT_OF_MEMORY,
    INVALID_PARAMETER = QUIC_STATUS_INVALID_PARAMETER,
    INVALID_STATE = QUIC_STATUS_INVALID_STATE,
    NOT_SUPPORTED = QUIC_STATUS_NOT_SUPPORTED,
    NOT_FOUND = QUIC_STATUS_NOT_FOUND,
    BUFFER_TOO_SMALL = QUIC_STATUS_BUFFER_TOO_SMALL,
    HANDSHAKE_FAILURE = QUIC_STATUS_HANDSHAKE_FAILURE,
    ABORTED = QUIC_STATUS_ABORTED,
    ADDRESS_IN_USE = QUIC_STATUS_ADDRESS_IN_USE,
    INVALID_ADDRESS = QUIC_STATUS_INVALID_ADDRESS,
    CONNECTION_TIMEOUT = QUIC_STATUS_CONNECTION_TIMEOUT,
    CONNECTION_IDLE = QUIC_STATUS_CONNECTION_IDLE,
    UNREACHABLE = QUIC_STATUS_UNREACHABLE,
    INTERNAL_ERROR = QUIC_STATUS_INTERNAL_ERROR,
    CONNECTION_REFUSED = QUIC_STATUS_CONNECTION_REFUSED,
    PROTOCOL_ERROR = QUIC_STATUS_PROTOCOL_ERROR,
    VER_NEG_ERROR = QUIC_STATUS_VER_NEG_ERROR,
    TLS_ERROR = QUIC_STATUS_TLS_ERROR,
    USER_CANCELED = QUIC_STATUS_USER_CANCELED,
    ALPN_NEG_FAILURE = QUIC_STATUS_ALPN_NEG_FAILURE,
    STREAM_LIMIT_REACHED = QUIC_STATUS_STREAM_LIMIT_REACHED,
    ALPN_IN_USE = QUIC_STATUS_ALPN_IN_USE,
    CLOSE_NOTIFY = QUIC_STATUS_CLOSE_NOTIFY,
    BAD_CERTIFICATE = QUIC_STATUS_BAD_CERTIFICATE,
    UNSUPPORTED_CERTIFICATE = QUIC_STATUS_UNSUPPORTED_CERTIFICATE,
    REVOKED_CERTIFICATE = QUIC_STATUS_REVOKED_CERTIFICATE,
    EXPIRED_CERTIFICATE = QUIC_STATUS_EXPIRED_CERTIFICATE,
    UNKNOWN_CERTIFICATE = QUIC_STATUS_UNKNOWN_CERTIFICATE,
    REQUIRED_CERTIFICATE = QUIC_STATUS_REQUIRED_CERTIFICATE,
    CERT_EXPIRED = QUIC_STATUS_CERT_EXPIRED,
    CERT_UNTRUSTED_ROOT = QUIC_STATUS_CERT_UNTRUSTED_ROOT,
    CERT_NO_CERT = QUIC_STATUS_CERT_NO_CERT
#ifndef _WIN32
// posix need to undef the redefine.
#undef QUIC_STATUS
#endif
} QUIC_STATUS;
