#pragma once
#include <framework/util/log.h>

#include <framework/util/singleton.h>

namespace leopard {

class Logger : public framework::util::Singleton<Logger> {
   public:
    bool IsLogAllLevel() const { return log_level_ == 1; }
    bool IsLogDebugLevel() const { return log_level_ == 2 || log_level_ == 1; }
    void SetLogLevel(int32_t value) { 
		log_level_ = value; 
		if (log_level_ == 1) {
			GetLogger()->set_level(framework::util::log::LogLevel::trace);
			GetLogger()->flush_on(framework::util::log::LogLevel::trace);
		} else if (log_level_ == 2) {
            GetLogger()->set_level(framework::util::log::LogLevel::debug);
            GetLogger()->flush_on(framework::util::log::LogLevel::debug);
        }
    }
	int32_t GetLogLevel() const { return log_level_; }
	framework::util::log::LoggerPtr GetLogger() { return framework::util::log::Logger::defaultLogger(section_); }
	void SetSection(const std::string &section) { section_ = section; }

   private:
    int32_t log_level_{0};
	std::string section_{"Leopard"};
};

}  // namespace leopard
#define LEOPARD_LOG_TRACE(format, ...)                             \
    if (leopard::Logger::GetInstance()->IsLogAllLevel())           \
        leopard::Logger::GetInstance()->GetLogger()->trace(format, \
                                                           ##__VA_ARGS__);
#define LEOPARD_LOG_DEBUG(format, ...)                             \
    if (leopard::Logger::GetInstance()->IsLogDebugLevel())           \
        leopard::Logger::GetInstance()->GetLogger()->debug(format, \
                                                           ##__VA_ARGS__);
#define LEOPARD_LOG_TRACE2(format1, format2, ...) \
    if (leopard::Logger::GetInstance()->IsLogAllLevel()) {         \
		std::string format = format1; \
		format += format2; \
		leopard::Logger::GetInstance()->GetLogger()->trace(format.c_str(), ##__VA_ARGS__); \
	} 
#define LEOPARD_LOG_INFO(format, ...) \
    leopard::Logger::GetInstance()->GetLogger()->info(format, ##__VA_ARGS__);
#define LEOPARD_LOG_WARN(format, ...) \
    leopard::Logger::GetInstance()->GetLogger()->warn(format, ##__VA_ARGS__);
#define LEOPARD_LOG_ERROR(format, ...) \
    leopard::Logger::GetInstance()->GetLogger()->error(format, ##__VA_ARGS__);
#define LEOPARD_LOG_FATAL(format, ...) \
    leopard::Logger::GetInstance()->GetLogger()->fatal(format, ##__VA_ARGS__);
