#! /bin/sh

set -e
apk add --upgrade sudo alpine-sdk

git config --global user.name "Microsoft QUIC Team"
git config --global user.email "<EMAIL>"

# Add the packaging user to the abuild group
adduser -D packaging -G abuild

# Give the packaging user sudo access
echo "packaging ALL=(ALL)       NOPASSWD: ALL" > /etc/sudoers.d/packaging

mkdir -p /var/cache/distfiles
chmod a+w /var/cache/distfiles

mkdir -p /home/<USER>/github-actions/packages/
chown -R packaging:abuild /home/<USER>/github-actions/packages/

mkdir -p /home/<USER>/tools
cp /msquic/APKBUILD /home/<USER>/tools
chown -R packaging:abuild /home/<USER>/tools

su packaging -c "abuild-keygen -n"
find /home/<USER>/.abuild -name '*.rsa' -exec /msquic/scripts/alpine-configure-packaging-key.sh {} \;

# msquic is using submodules and we need to get them inside
cd /home/<USER>/tools
su packaging -c "abuild -r"

cp /home/<USER>/packages/packaging/**/*.apk /artifacts
