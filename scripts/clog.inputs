../src/platform/selfsign_openssl.c
../src/platform/datapath_kqueue.c
../src/platform/crypt.c
../src/platform/datapath_winkernel.c
../src/platform/datapath_epoll.c
../src/platform/tls_schannel.c
../src/platform/selfsign_capi.c
../src/platform/cert_capi.c
../src/platform/platform_posix.c
../src/platform/storage_winkernel.c
../src/platform/tls_quictls.c
../src/platform/tls_openssl.c
../src/platform/platform_winkernel.c
../src/platform/certificates_posix.c
../src/platform/hashtable.c
../src/platform/datapath_winuser.c
../src/platform/datapath_linux.c
../src/platform/datapath_unix.c
../src/platform/datapath_xplat.c
../src/platform/datapath_raw_dpdk.c
../src/platform/datapath_raw_socket.c
../src/platform/datapath_raw_socket_win.c
../src/platform/datapath_raw_socket_linux.c
../src/platform/datapath_raw_xdp_win.c
../src/platform/datapath_raw_xdp_linux.c
../src/platform/datapath_raw_win.c
../src/platform/datapath_raw_linux.c
../src/platform/datapath_raw.c
../src/platform/crypt_bcrypt.c
../src/platform/platform_winuser.c
../src/platform/toeplitz.c
../src/platform/pcp.c
../src/platform/storage_winuser.c
../src/platform/storage_posix.c
../src/platform/crypt_openssl.c
../src/platform/platform_worker.c
../src/perf/bin/histogram/hdr_histogram.c
../src/core/api.c
../src/core/range.c
../src/core/injection.c
../src/core/sent_packet_metadata.c
../src/core/datagram.c
../src/core/cubic.c
../src/core/bbr.c
../src/core/packet_space.c
../src/core/registration.c
../src/core/send.c
../src/core/path.c
../src/core/settings.c
../src/core/connection.c
../src/core/packet_builder.c
../src/core/version_neg.c
../src/core/lookup.c
../src/core/worker.c
../src/core/connection_pool.c
../src/core/congestion_control.c
../src/core/crypto_tls.c
../src/core/binding.c
../src/core/send_buffer.c
../src/core/stream.c
../src/core/ack_tracker.c
../src/core/frame.c
../src/core/recv_buffer.c
../src/core/crypto.c
../src/core/packet.c
../src/core/timer_wheel.c
../src/core/listener.c
../src/core/loss_detection.c
../src/core/stream_set.c
../src/core/stream_send.c
../src/core/stream_recv.c
../src/core/operation.c
../src/core/mtu_discovery.c
../src/core/configuration.c
../src/core/partition.c
../src/core/library.c
../src/bin/winuser_fuzz/dllmain.c
../src/bin/linux/init.c
../src/bin/winuser/dllmain.c
../src/bin/winkernel/driver.c
../src/bin/winkernel/msquicpcw.c
../src/bin/winkernel/nmrprovider.c
../src/bin/static/empty.c
../src/core/operation.h
../src/core/stream.h
../src/core/connection.h
../src/test/lib/TestHelpers.h
../src/test/lib/TestStream.cpp
../src/test/lib/DataTest.cpp
../src/test/lib/PathTest.cpp
../src/test/lib/TestListener.cpp
../src/test/lib/EventTest.cpp
../src/test/lib/ApiTest.cpp
../src/test/lib/DrillDescriptor.cpp
../src/test/lib/BasicTest.cpp
../src/test/lib/TestConnection.cpp
../src/test/lib/MtuTest.cpp
../src/test/lib/HandshakeTest.cpp
../src/test/lib/QuicDrill.cpp
../src/test/lib/DatagramTest.cpp
../src/test/lib/OwnershipTest.cpp
../src/perf/lib/PerfClient.cpp
../src/perf/lib/PerfServer.cpp
../src/perf/lib/CMakeLists.txt
../src/perf/lib/Tcp.cpp
../src/core/unittest/SettingsTest.cpp
../src/core/unittest/SpinFrame.cpp
../src/core/unittest/SlidingWindowExtremumTest.cpp
../src/core/unittest/RangeTest.cpp
../src/core/unittest/RecvBufferTest.cpp
../src/core/unittest/VarIntTest.cpp
../src/core/unittest/CMakeLists.txt
../src/core/unittest/FrameTest.cpp
../src/core/unittest/TicketTest.cpp
../src/core/unittest/PacketNumberTest.cpp
../src/core/unittest/TransportParamTest.cpp
../src/core/unittest/main.cpp
../src/core/unittest/VersionNegExtTest.cpp
../src/core/unittest/PartitionTest.cpp
../src/platform/unittest/TlsTest.cpp
../src/platform/unittest/PlatformTest.cpp
../src/platform/unittest/CryptTest.cpp
../src/platform/unittest/DataPathTest.cpp
../src/platform/unittest/ToeplitzTest.cpp
../src/platform/unittest/StorageTest.cpp
../src/test/bin/quic_gtest.h
../src/test/bin/quic_gtest.cpp
../src/perf/bin/histogram/hdr_histogram.h
../src/perf/bin/histogram/hdr_histogram.c
../src/perf/bin/drvmain.cpp
../src/tools/interop/interop.cpp
../src/tools/interop/interop.h
